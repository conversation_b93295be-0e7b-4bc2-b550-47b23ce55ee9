
对⽐相似度检索算法的实验需求 
⼀、实验脚本需求⽂档 
1)、总体流程
1.	随机抽取原始语料
调⽤向量数据库 API，随机拉取⼀个⽬标实体及其相关联的实体（均为 JSON，含唯⼀ id）。
2.	 ⽣成问答对 将上述原始语料交给“问题⽣成模型”，输出⼀条基于该语料的问答对：


{
"question": "...", "reference_answer": "..."
}


3.	相似度检索
使⽤待评估的相似度检索算法，以 question 为查询，向同⼀向量库拉取若⼲最相似实 体（含 id）。
4.	模型回答
将检索结果＋ question ⼀并发给“回答模型”，输出 predicted_answer。 5.	评估打分
将【原始语料】、 question、 reference_answer、 predicted_answer 传⼊“评估模 型”，按 三个维度 独⽴打分（0–10），输出最终评估报告。




2)、模块划分

1.	 DataFetcherRandom 功能：
封装向量库 API，以随机抽样模式拉取原始语料（实体+关系实体）。 输⼊：
sample_size (int, 可选)：本次随机返回的条⽬数，默认 1。
输出：
列表 of { 'id':..., 'entity_description':...}
2.	 DataFetcherSimilarity 功能：
封装向量库 API，以相似度检索模式拉取最相似的上下⽂（实体＋关系）。
输⼊：
question (string)：待检索的问题⽂本

输出：
列表 of { 'id':..., 'distance':..., entity: {'description'：...} }

3.	 QAItemGenerator 功能：调⽤问题⽣成模型，基于原始语料构造问答对。 输⼊：原始语料 JSON 列表
输出： { 'question':..., 'reference_answer':... } 4.	AnswerGenerator
功能：调⽤回答模型，基于检索结果回答问题。
输⼊： { 'question':..., 'retrieved_context':... } 输出： predicted_answer
5.	Evaluator
功能：调⽤评估模型，对⽐ reference_answer 与 predicted_answer，并结合原始语 料，输出三维度评分。
输⼊：


{
"original_context": [...], "question": "...", "reference_answer": "...", "predicted_answer": "..."
}


输出：


{
"scores": {
"context_relevance": X,	// 0–10 "information_coverage": Y, // 0–10 "answer_accuracy": Z	// 0–10
} }
3)、三⼤评分维度设计

1.	 上下⽂相关度（Context Relevance） 衡量检索到的实体/关系与 question 主题的相关性。
10 分：所有检索项均切中要点；0 分：与问题主题⽆关。
2.	信息覆盖度（Information Coverage）
衡量检索结果中，能覆盖原始语料中解决问题所需关键信息的⽐例。
计算⽅式：检索结果中 id 与原始语料 id 的交集占“原始语料条⽬数”的⽐例，映射到 0–10 分。
3.	答案准确度（Answer Accuracy）
衡量 predicted_answer 与 reference_answer 在事实和要点上的⼀致性。 考察：关键事实是否正确、缺失程度、错误信息数量。

4)、⽇志 & 结果导出

⽇志：每⼀轮的原始输⼊、模型调⽤返回、评分明细，建议采⽤ JSONL 存储。 导出：最终可⽣成 CSV/Markdown 报表，⽅便可视化。

！

⼆、客观评分算法 1）、定义

G：初始随机抽取到的“真实”JSON条⽬ ID 集合 R：相似度检索算法返回的 JSON 条⽬ ID 集合

举例：
G = {A, B, C, D}
R = {B, C, D, E, F}




2）、IoU 计算与映射

1.	交集⼤⼩

I = |G ∩ R|
2.	并集⼤⼩

U = |G ∪ R|
3.	交并⽐（IoU）

IoU = U 
4.	映射到 0–10 分

S = round(10 × IoU, 2)




3）、算法流程


输⼊：真实 ID 集合 G，检索返回 ID 集合 R 输出：⼀个 0–10 分的客观准确度分数 S

步骤：
1. 计算 I = |G ∩ R| 2. 计算 U = |G ∪ R| 3. 计算 IoU = I / U
4. 计算 S = round(10 * IoU, 2) 5. 返回 S





4）、示例

G = {A, B, C, D} → |G| = 4
R = {B, C, D, E, F} → |R| = 5 交集 I = {B, C, D} → I = 3
并集 U = {A, B, C, D, E, F} → U = 6 IoU = 3 / 6 = 0.5
映射分数 S = 10 × 0.5 = 5.0




5）、说明

当 R 完全命中 G 且⽆多余（即 R=G）时，IoU=1 → S=10。 当⽆交集（I=0）时，IoU=0 → S=0。
IoU 同时惩罚“少检索”（遗漏真实项）和“多检索”（多余项）。

三、主观评分标准
{
"scores": {
"context_relevance": X,	// 0–10 "information_coverage": Y, // 0–10 "answer_accuracy": Z	// 0–10
} }


相关度、覆盖率、准确度

四、具体功能设计

1) 模型管理

⻚⾯⼊⼝：在实验脚本界⾯提供“模型管理”标签⻚/模块。 ⽀持模型类型：
问题⽣成模型（Question Generator） 回答模型（Answer Generator） 评估模型（Evaluator）
配置字段（每种模型均相同）：
1. 名称（string）：⾃定义展示名
2. API Endpoint（URL）：HTTP 接⼝地址 3. API Key（string）：鉴权密钥
4. Prompt 模板（text area）：预设的提示词/模板变量
操作：新增 / 编辑 / 删除 / 保存（点击“测试调⽤”可验证连通性并展示示例返回）




2) 检索算法接⼝配置

⻚⾯⼊⼝：在实验脚本界⾯提供“检索算法配置”标签⻚/模块。 配置项：
1.	随机抽样接⼝（DataFetcherRandom） 名称（string）
Endpoint（URL） ⽅法（GET/POST） 示例参数（JSON/text）
2.	相似度检索接⼝（DataFetcherSimilarity） 名称（string）
Endpoint（URL） ⽅法（POST） 必填参数：
question 或 query_embedding
top_k

distance_threshold 示例请求体（JSON）
3.	新增检索算法 点击“添加算法”，可按相同模板新增任意多种检索算法接⼝配置
操作：新增 / 编辑 / 删除 / 测试调⽤（展示返回的 items 列表预览）




3) 执⾏设置

运⾏参数模块：在主⾯板展示，运⾏前必须配置以下参数： 1. 运⾏次数（int）：实验轮次，必填
2. 勾选算法：可多选已配置的 相似度检索算法，每选中⼀条将在实验中独⽴执⾏并产⽣⼀ 组评分
操作按钮： “开始运⾏”：触发所有选中算法的批量实验 “停⽌运⾏”：中途可⼿动中断




4) 结果可视化

数据结构：
对于每个算法 Aᵢ，在每轮 r 均会产⽣： 客观评分 objective_score(r, Aᵢ) 主观评分 subjective_score(r, Aᵢ)
折线图展示：
X 轴：执⾏轮次（1…N） Y 轴：评分值（0–10） 每个算法画 两条折线：
实⼼线（或默认样式）：客观评分轨迹 虚线（或区别样式）：主观评分轨迹
图例：展示算法名称及“客观”/“主观”标识
图⽚要可以导出下载

多算法对⽐：
若选中 2 个算法，则共绘制 4 条折线； ⽀持最多并⾏展示 5 种算法（即最多 10 条折线）；