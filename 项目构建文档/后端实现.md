# 相似度检索算法实验平台 - 后端开发工作流程 (WORKFLOW)

## 0. 引言

本文档详细规划了"相似度检索算法实验平台"后端系统的开发工作流程。它基于提供的项目设计文档，旨在将开发过程分解为可管理、可跟踪的阶段和任务。每个任务都包含明确的完成标准（检查点），并且核心组件的接口（方法签名）将被详细定义，以确保模块间的顺利集成。

**外部依赖确认：**
*   `llm_api_integrator` 模块已完成，其接口（`LLMInterface` 和 `LLMFactory`）将按定义使用。
*   向量数据库 API（至少一个，具体待配置）
*   知识图谱系统 API（用于抽取原始语料和接收问答对进行检索）

## 1. 通用数据模型定义 (Pydantic)

在开始各模块开发前，优先定义核心数据结构。这些模型将在各个服务和API层之间传递。

**文件位置:** `backend/app/models/common.py` (路径已修正)

**任务 1.1: 定义核心数据模型**
*   **描述:** 根据平台需求，定义用于配置、数据处理流程及结果存储的Pydantic模型。
*   **模型列表 (初步):**
    *   `CorpusItemInput(BaseModel)`: 用于从知识图谱系统接收原始语料。
        *   `content: str`
        *   `metadata: Optional[Dict[str, Any]] = None`
    *   `CorpusItem(CorpusItemInput)`: 存储的原始语料，包含ID。
        *   `id: str`（由知识图谱系统提供或内部生成）
    *   `LLMModelConfigInput(BaseModel)`: LLM配置输入。
        *   `role: str`（枚举: "qa_generator", "answer_predictor", "evaluator"）
        *   `name: str`（用户定义的配置名称）
        *   `provider: str`（对应 `llm_api_integrator` 的 `provider_name`）
        *   `model_name: str`（对应 `llm_api_integrator` 的 `model_name`）
        *   `api_key: Optional[str] = None`（新增：用于存储API密钥，明文存储，仅内部使用）
        *   `prompt_template: str`
        *   `additional_params: Optional[Dict[str, Any]] = {}`（如 `temperature`, `max_tokens`）
        *   `(示例见 common.py 中此模型的 model_config.json_schema_extra.examples)`
    *   `LLMModelConfig(LLMModelConfigInput)`: 存储的LLM配置，包含ID和API密钥（内部使用）。
        *   `id: str`（数据库生成的UUID）
        *   `api_key: Optional[str] = None`（新增：存储的API密钥）
        *   `created_at: datetime` (内部使用)
        *   `updated_at: Optional[datetime] = None` (内部使用)
        *   `created_at_local_str: Optional[str]` (API响应中使用，例如 "2023-10-26 18:00:00 CST+0800")
        *   `updated_at_local_str: Optional[str]` (API响应中使用，例如 "2023-10-26 18:05:00 CST+0800")
    *   `LLMModelConfigResponse(BaseModel)`: 用于API响应的LLM配置，不含API密钥。
        *   `id: str`
        *   `role: str`
        *   `name: str`
        *   `provider: str`
        *   `model_name: str`
        *   `prompt_template: str`
        *   `additional_params: Optional[Dict[str, Any]] = {}`
        *   `created_at: Optional[str]` (API响应中使用，例如 "2023-10-26 18:00:00 CST+0800")
        *   `updated_at: Optional[str]` (API响应中使用，例如 "2023-10-26 18:05:00 CST+0800")
        *   `(示例见 common.py 中此模型的 model_config.json_schema_extra.examples)`
    *   `CorpusExtractionApiConfig(BaseModel)`: 随机抽样接口配置。
        *   `endpoint: HttpUrl = Field(description="随机抽样API的端点URL")`
        *   `method: str = Field(default="POST", description="与随机抽样API交互的HTTP方法 (如 GET, POST)")`
        *   `headers: Optional[Dict[str, str]] = Field(default_factory=dict, description="与随机抽样API交互时使用的HTTP请求头")`
        *   `params: Optional[Dict[str, Any]] = Field(default_factory=dict, description="与API交互时使用的参数，对于GET会成为URL查询参数，对于某些POST可能是表单数据")`
        *   `# 备注: 运行时，编排器可能会向此 params 字典中动态添加或覆盖参数，例如 'count'。`
    *   `SimilaritySearchApiConfig(BaseModel)`: 相似度检索接口配置。
        *   `endpoint: HttpUrl = Field(description="相似度检索API的端点URL")`
        *   `method: str = Field(default="POST", description="与相似度检索API交互的HTTP方法 (通常为POST)")`
        *   `headers: Optional[Dict[str, str]] = Field(default_factory=dict, description="与相似度检索API交互时使用的HTTP请求头")`
        *   `body: Dict[str, Any] = Field(description="与相似度检索API交互时使用的HTTP请求体，包含 algorithm_name, top_k 等固定键以及其他自定义参数")`
        *   `# 备注: 'question' 的值由编排器动态填充，并作为键名添加到 body 中。`
        *   `# 'algorithm_name', 'top_k', 'distance_threshold' 等核心参数应在 body 中定义。`
    *   `RetrievalAlgorithmConfigInput(BaseModel)`: 检索算法配置输入。
        *   `name: str = Field(description="此组检索配置的名称")`
        *   `corpus_extraction_api: CorpusExtractionApiConfig = Field(description="随机抽样接口配置 (DataFetcherRandom)")`
        *   `similarity_search_api: SimilaritySearchApiConfig = Field(description="相似度检索接口配置 (DataFetcherSimilarity)")`
        *   `model_config` (Pydantic v2) 或 `Config` (Pydantic v1) 中定义了以下示例，用于FastAPI文档：
            ```json
            {
                "name": "默认知识库与向量检索（示例）",
                "corpus_extraction_api": {
                    "endpoint": "http://localhost:8001/kb/corpus/sample",
                    "method": "GET",
                    "headers": {"Authorization": "Bearer corpus_token"},
                    "params": {
                        "count": "1",
                        "top": "3"
                    }
                },
                "similarity_search_api": {
                    "endpoint": "http://localhost:8001/kb/search/similarity",
                    "method": "POST",
                    "headers": {"Authorization": "Bearer search_token"},
                    "body": {
                        "algorithm_name": "faiss_flat_ip_on_bert_base_chinese",
                        "distance_threshold": 0.8,
                        "custom_param1": "value1",
                        "top_k": 10
                    }
                }
            }
            ```
    *   `RetrievalAlgorithmConfig(RetrievalAlgorithmConfigInput)`: 存储的检索算法配置，包含ID。
        *   `id: str`（数据库生成的UUID）
        *   `created_at: Optional[datetime] = Field(default_factory=datetime.utcnow, description="创建时间 (内部使用)")`
        *   `updated_at: Optional[datetime] = Field(default_factory=datetime.utcnow, description="更新时间 (内部使用)")`
        *   `created_at_local_str: Optional[str]` (API响应中使用，例如 "2023-10-26 18:00:00 CST+0800")
        *   `updated_at_local_str: Optional[str]` (API响应中使用，例如 "2023-10-26 18:05:00 CST+0800")
        *   `(注意：model_config 示例已在实际代码中更新，其示例中的 created_at/updated_at 应为格式化后的字符串)`
    *   `QAItem(BaseModel)`: 生成的问答对。
        *   `original_corpus_item_id: str`
        *   `original_corpus_content: str`（可选，用于LLM3评估时参考）
        *   `question: str`
        *   `generated_answer: str`（由LLM1生成）
    *   `SearchResultItem(BaseModel)`: 单个检索结果。
        *   `id: str`（检索结果的文档ID）
        *   `content: Optional[str] = None`（检索结果的文本内容）
        *   `score: Optional[float] = None`
    *   `SimilaritySearchResult(BaseModel)`: 针对一个问题的检索结果集合。
        *   `question_item: QAItem`（包含原始问题信息）
        *   `retrieved_items: List[SearchResultItem]`
        *   `ground_truth_ids: List[str]`（用于IoU计算，通常是 `[QAItem.original_corpus_item_id]`）
        *   `selection_method: Optional[str] = None` (标记检索方法 "greedy" 或 "simple")
        *   `k_value: Optional[int] = None` (用于存储Greedy阶段返回的k或Simple阶段使用的k)
    *   `PredictedAnswerInput(BaseModel)`:
        *   `similarity_search_result: SimilaritySearchResult`
    *   `PredictedAnswer(PredictedAnswerInput)`: LLM2的预测结果。
        *   `predicted_answer_text: str`
    *   `EvaluationInput(BaseModel)`: 评估模型的输入。
        *   `qa_item: QAItem`
        *   `similarity_search_result: SimilaritySearchResult`
        *   `predicted_answer: PredictedAnswer`
    *   `EvaluationScores(BaseModel)`: LLM3的主观评估打分。
        *   `context_relevance: float`（1-10）
        *   `information_coverage: float`（1-10）
        *   `answer_accuracy: float`（1-10）
    *   `IoUScoreResult(BaseModel)`: 客观IoU评分。
        *   `retrieved_ids: List[str]`
        *   `ground_truth_ids: List[str]`
        *   `intersection_count: int`
        *   `union_count: int`
        *   `iou: float`
        *   `score: float`（0-10）
    *   `ObjectiveInformationCoverageResult(BaseModel)`: 需求文档定义的客观信息覆盖度评分。
        *   `retrieved_ids: List[str]`
        *   `ground_truth_ids: List[str]`
        *   `intersection_count: int`
        *   `ground_truth_count: int` # 原始语料条目数
        *   `coverage_ratio: float` # intersection_count / ground_truth_count
        *   `score: float` # 0-10分，按需求文档映射
    *   `ExperimentLogEntry(BaseModel)`: 单条日志记录。
        *   `timestamp: str` (API响应中使用，例如 "2023-10-26 18:00:00 CST+0800")
        *   `step_name: str`
        *   `(备注: 内部存储为 datetime 对象，通过 timestamp_raw 访问，API响应时转换并以 'timestamp' 为键名)`
    *   `ExperimentTaskConfigInput(BaseModel)`: 实验任务配置输入。
        *   `name: str`
        *   `description: Optional[str] = None`
        *   `qa_generator_model_config_id: str`
        *   `answer_predictor_model_config_id: str`
        *   `evaluator_model_config_id: str`
        *   `algorithm_config_id: str`
        *   `default_max_corpus_items: int = Field(10, gt=0)`
        *   `(示例见 common.py 中此模型的 model_config.json_schema_extra.examples)`
    *   `ExperimentTaskConfig(ExperimentTaskConfigInput)`: 存储的实验任务配置，包含ID。
        *   `id: str` # 数据库生成的UUID
        *   `created_at: Optional[datetime]` (内部使用)
        *   `updated_at: Optional[datetime] = None` (内部使用)
        *   `created_at_local_str: Optional[str]` (API响应中使用，例如 "2023-10-26 18:00:00 CST+0800")
        *   `updated_at_local_str: Optional[str]` (API响应中使用，例如 "2023-10-26 18:05:00 CST+0800")
        *   `(示例见 common.py 中此模型的 model_config.json_schema_extra.examples)`
    *   `ExperimentTaskConfigResponse(BaseModel)`: 用于API响应的实验任务配置。
        *   `id: str`
        *   `name: str`
        *   `description: Optional[str] = None`
        *   `qa_generator_model_config_id: str` 
        *   `answer_predictor_model_config_id: str` 
        *   `evaluator_model_config_id: str` 
        *   `algorithm_config_id: str` 
        *   `qa_generator_model_name: Optional[str] = None` # 由服务层填充
        *   `answer_predictor_model_name: Optional[str] = None` # 由服务层填充
        *   `evaluator_model_name: Optional[str] = None` # 由服务层填充
        *   `algorithm_name: Optional[str] = None` # 由服务层填充
        *   `default_max_corpus_items: int`
        *   `created_at: Optional[str]` (API响应中使用，例如 "2023-10-26 18:00:00 CST+0800")
        *   `updated_at: Optional[str]` (API响应中使用，例如 "2023-10-26 18:05:00 CST+0800")
        *   `(示例见 common.py 中此模型的 model_config.json_schema_extra.examples)`
    *   `ExperimentRunStatus(str, Enum)`: 实验运行的详细状态。
        *   `PENDING = "PENDING"`
        *   `CONFIG_VALIDATION = "CONFIG_VALIDATION"`
        *   `CORPUS_EXTRACTION = "CORPUS_EXTRACTION"`
        *   `QA_GENERATION = "QA_GENERATION"`
        *   `SIMILARITY_SEARCH = "SIMILARITY_SEARCH"`
        *   `ANSWER_PREDICTION = "ANSWER_PREDICTION"`
        *   `EVALUATION = "EVALUATION"`
        *   `COMPLETED = "COMPLETED"`
        *   `FAILED = "FAILED"`
        *   `CELERY_TASK_UNKNOWN = "CELERY_TASK_UNKNOWN"`
    *   `ExperimentRunReport(BaseModel)`: 单次实验运行的完整报告。
        *   `run_id: str`
        *   `experiment_task_config_id: Optional[str] = None` # 记录基于哪个任务配置运行
        *   `algorithm_config: RetrievalAlgorithmConfig`
        *   `qa_generator_config: LLMModelConfig`
        *   `answer_predictor_config: LLMModelConfig`
        *   `evaluator_config: LLMModelConfig`
        *   `raw_corpus_items: List[CorpusItem]`
        *   `generated_qa_items: List[QAItem]`
        *   `all_similarity_search_results: List[SimilaritySearchResult]`
        *   `all_predicted_answers: List[PredictedAnswer]`
        *   `all_evaluation_scores: List[EvaluationScores]`
        *   `all_iou_scores: List[IoUScoreResult]`
        *   `all_objective_information_coverage_scores: List[ObjectiveInformationCoverageResult] # 新增：需求文档定义的客观覆盖度评分
        *   `overall_avg_iou_score: Optional[float] = None`
        *   `overall_avg_objective_information_coverage_score: Optional[float] = None` # 新增
        *   `overall_avg_context_relevance: Optional[float] = None`
        *   `overall_avg_information_coverage: Optional[float] = None`
        *   `overall_avg_answer_accuracy: Optional[float] = None`
        *   `full_log_path: Optional[str] = None`
        *   `status: ExperimentRunStatus = ExperimentRunStatus.PENDING`
        *   `status_message: Optional[str] = None` # 用于存储错误信息或当前状态的描述
        *   `created_at: datetime = Field(default_factory=datetime.utcnow)`
        *   `updated_at: datetime = Field(default_factory=datetime.utcnow)`
        *   `(此模型结构复杂，详细字段及嵌套结构请参考 common.py。一个概要性示例已在代码的 model_config 中提供。)`
    *   `SingleExperimentTaskSubmissionResponse(BaseModel)`: 单个实验任务（Celery Task）提交后的响应。
        *   `celery_task_id: str`
        *   `run_id: str`
        *   `status_url: str` # 指向Celery任务状态查询的URL
        *   `report_url: str` # 指向该run_id的实验报告查询URL
    *   `BatchExperimentRunResponse(BaseModel)`: 批量运行实验的API响应。
        *   `requested_repetitions: int`
        *   `base_custom_run_id: Optional[str] = None`
        *   `submissions: List[SingleExperimentTaskSubmissionResponse]`
        *   `(示例见 common.py 中此模型的 model_config.json_schema_extra.examples)`
*   **完成检查点:**
    *   [x] `backend/models/common.py` 文件已创建并包含上述所有Pydantic模型。
    *   [x] 模型字段类型正确，可选字段已标记。
    *   [x] 模型定义通过Pydantic的校验。

## 2. 后端基础架构与配置管理

**目标:** 搭建FastAPI应用，实现模型配置和算法配置的CRUD管理。

**Phase 2.1: 项目设置与数据库**
*   **任务 2.1.1: 初始化FastAPI项目**
    *   **描述:** 创建项目结构，安装 FastAPI、Uvicorn、Pydantic、SQLAlchemy（或选择的ORM/DB driver）、`python-jose`（用于简单API密钥保护）、`passlib`。
    *   **文件结构 (初步):**
        ```
        backend/
        ├── app/
        │   ├── __init__.py
        │   ├── main.py             # FastAPI应用实例
        │   ├── core/               # 核心配置和安全
        │   │   ├── __init__.py
        │   │   ├── config.py       # 配置（如DB URL, 日志路径等）
        │   │   └── security.py     # API密钥工具、密码处理等
        │   ├── models/             # Pydantic模型和数据库ORM模型
        │   │   ├── __init__.py
        │   │   ├── common.py       # API和服务层使用的数据模型 (Pydantic)
        │   │   └── db_models.py    # 数据库表对应的ORM模型 (SQLAlchemy)
        │   ├── apis/
        │   │   ├── __init__.py
        │   │   ├── deps.py         # FastAPI依赖注入项
        │   │   └── v1/             # API版本v1
        │   │       ├── __init__.py
        │   │       ├── endpoints/    # API路由处理
        │   │       │   ├── __init__.py
        │   │       │   ├── llm_configs.py
        │   │       │   ├── algorithm_configs.py
        │   │       │   └── experiments_api.py  # 实验运行与结果API
        │   │       └── schemas.py      # API请求/响应体Pydantic模型 (可复用common.py或单独定义)
        │   ├── services/           # 业务逻辑服务层
        │   │   ├── __init__.py
        │   │   ├── config_manager_service.py # LLM和算法配置管理
        │   │   ├── api_caller_service.py       # 封装外部API调用 (LLM, 知识图谱, 检索算法)
        │   │   ├── logger_service.py           # 实验日志记录服务
        │   │   ├── result_storage_service.py   # 实验结果存储服务
        │   │   └── experiment_orchestrator_service.py # 核心实验编排服务
        │   ├── crud/                 # 数据库CRUD操作封装
        │   │   ├── __init__.py
        │   │   ├── crud_llm_config.py
        │   │   └── crud_algorithm_config.py
        │   └── utils/                # 通用工具函数
        │       ├── __init__.py
        │       └── objective_scorer.py # 客观评分计算工具 (如IoU)
        ├── alembic/                # 数据库迁移脚本 (如使用Alembic)
        │   └── ...
        ├── tests/                  # 测试代码
        │   ├── __init__.py
        │   ├── conftest.py         # Pytest共享fixtures
        │   ├── unit/               # 单元测试
        │   │   ├── __init__.py
        │   │   ├── services/
        │   │   │   └── test_experiment_orchestrator_service.py # 示例
        │   │   └── utils/
        │   │       └── test_objective_scorer.py      # 示例
        │   └── integration/        # 集成测试
        │       ├── __init__.py
        │       └── apis/
        │           └── v1/
        │               └── test_experiments_api.py   # 示例
        ├── .env.example            # 环境变量示例文件
        ├── .gitignore              # Git忽略文件配置
        ├── README.md               # 项目说明文档
        ├── requirements.txt        # Python依赖包列表
        ```
    *   **完成检查点:**
        *   [ ] FastAPI应用可启动（`uvicorn app.main:app --reload`）。
        *   [ ] 基本项目结构已创建。

*   **任务 2.1.2: 数据库模式设计与SQLAlchemy模型**
    *   **描述:** 基于Pydantic模型，设计数据库表结构并创建SQLAlchemy模型。
    *   **文件:** `app/models/db_models.py`
    *   **主要表:** `llm_configurations`（包含 `api_key` 字段用于明文存储密钥）、`retrieval_algorithm_configurations`（其结构已更新，包含 `corpus_extraction_api` 和 `similarity_search_api` 作为JSON列，以存储各自的详细API配置）、`experiment_runs`、`experiment_results`。
    *   **完成检查点:**
        *   [x] SQLAlchemy模型定义完成，与Pydantic模型对应。
        *   [ ] （如用Alembic）初始数据库迁移脚本生成并成功应用。

**Phase 2.2: 配置管理服务与API（config_manager_service）**
*   **文件:** `app/services/config_manager_service.py`、`app/crud/crud_*.py`、`app/apis/v1/endpoints/*.py`
*   **任务 2.2.1: LLM模型配置管理**
    *   **描述:** 实现LLM模型配置的增删改查（CRUD）功能。API密钥将作为配置的一部分明文存储在数据库中，仅供内部使用，不在API响应中暴露。
    *   **`crud_llm_config.py` 方法签名（示例）:**
        ```python
        from sqlalchemy.orm import Session
        from app.models import common, db_models

        def get_llm_config(db: Session, config_id: str) -> Optional[db_models.LLMConfiguration]: ...
        def get_llm_configs(db: Session, skip: int = 0, limit: int = 100) -> List[db_models.LLMConfiguration]: ...
        def create_llm_config(db: Session, config: common.LLMModelConfigInput) -> db_models.LLMConfiguration: ... # 会保存 config.api_key
        # update_llm_config, delete_llm_config
        ```
    *   **`config_manager_service.py` 方法签名（示例）:**
        ```python
        from app.models import common

        class ConfigManagerService:
            # 业务逻辑封装CRUD
            async def create_llm_model_config(self, config_in: common.LLMModelConfigInput) -> common.LLMModelConfig: ...
            async def get_llm_model_config(self, config_id: str) -> Optional[common.LLMModelConfig]: ... # 内部使用，可包含key
            async def get_llm_model_config_for_response(self, config_id: str) -> Optional[common.LLMModelConfigResponse]: ... # 用于API响应
            async def get_all_llm_model_configs_for_response(self) -> List[common.LLMModelConfigResponse]: ... # 用于API响应
            async def get_api_key_for_config(self, config_id: str) -> Optional[str]: ... # 新增：获取指定配置的API密钥
            # update, delete
        ```
    *   **API端点（llm_configs.py）:**
        *   `POST /llm-configs/`：创建（请求体: `LLMModelConfigInput`，响应: `LLMModelConfigResponse`）
        *   `GET /llm-configs/`：获取列表（响应: `List[LLMModelConfigResponse]`）
        *   `GET /llm-configs/{config_id}`：获取单个（响应: `LLMModelConfigResponse`）
        *   `PUT /llm-configs/{config_id}`：更新（请求体: `LLMModelConfigInput`，响应: `LLMModelConfigResponse`）
        *   `DELETE /llm-configs/{config_id}`：删除（响应: `common.MessageResponse`，状态码: `200 OK`，例如: `{"message": "LLM配置 '{config_id}' 已成功删除。"}`）
    *   **完成检查点:**
        *   [ ] CRUD操作逻辑在`crud_llm_config.py`中实现，包括`api_key`的直接存取。
        *   [ ] 服务层方法在`config_manager_service.py`中实现，区分内部模型和API响应模型。
        *   [ ] FastAPI端点定义完成，并通过Swagger UI测试。API响应不包含密钥。
        *   [ ] LLM配置数据（包括API密钥）能正确存入和读取数据库。

*   **任务 2.2.2: 检索算法配置管理**
    *   **描述:** 实现检索算法配置的CRUD功能。
    *   **方法签名和服务层:** 结构类似LLM模型配置管理。使用新的 `RetrievalAlgorithmConfigInput` 和 `RetrievalAlgorithmConfig`。
        *   `crud_algorithm_config.py`
        *   `config_manager_service.py`（添加相应方法）
    *   **API端点（algorithm_configs.py）:**
        *   `POST /algorithm-configs/`：创建 (请求体: `RetrievalAlgorithmConfigInput`, 响应: `RetrievalAlgorithmConfig`)
        *   `GET /algorithm-configs/`：获取列表 (响应: `List[RetrievalAlgorithmConfig]`)
        *   `GET /algorithm-configs/{config_id}`：获取单个（响应: `RetrievalAlgorithmConfig`）
        *   `PUT /algorithm-configs/{config_id}`：更新（请求体: `RetrievalAlgorithmConfigInput`，响应: `RetrievalAlgorithmConfig`）
        *   `DELETE /algorithm-configs/{config_id}`：删除（响应: `common.MessageResponse`，状态码: `200 OK`，例如: `{"message": "检索算法配置 '{config_id}' 已成功删除。"}`）
        *   `POST /algorithm-configs/{config_id}/test`：分别测试配置中定义的"随机抽样接口"和"相似度检索接口"的连通性（例如，对每个端点发送一个简单的OPTIONS请求）。响应将包含对两个API的单独测试结果。
    *   **完成检查点:**
        *   [x] CRUD和服务层实现。
        *   [x] API端点测试通过。
        *   [x] 算法配置数据能正确存入和读取数据库。

*   **任务 2.2.3: 测试配置接口连通性（初步）**
    *   **描述:** 为LLM配置和算法配置添加测试接口。LLM配置可简单ping模型（如`llm_api_integrator`支持）；算法配置尝试连接其endpoint。
    *   **`config_manager_service.py` 方法签名（示例）:**
        ```python
        async def test_llm_model_config(self, config_id: str) -> Dict[str, Any]: # api_key从内部获取
            # 获取配置 (包含密钥)，用llm_api_integrator测试
            ...
        async def test_retrieval_algorithm_config(self, config_id: str) -> Dict[str, Any]:
            # 获取配置，分别尝试对 corpus_extraction_api.endpoint 和 similarity_search_api.endpoint
            # 发送 OPTIONS 请求。返回结果包含对两个API的测试详情。
            ...
        ```
    *   **API端点（追加到现有config API文件）:**
        *   `POST /llm-configs/{config_id}/test`（API密钥由服务层从数据库获取）
        *   `POST /algorithm-configs/{config_id}/test`
    *   **完成检查点:**
        *   [ ] 测试服务方法实现。
        *   [ ] API端点添加并可调用。

**Phase 2.3: 实验任务配置管理**
*   **文件:** `app/services/config_manager_service.py`、`app/crud/crud_experiment_task_config.py`、`app/apis/v1/endpoints/experiment_task_configs.py`
*   **任务 2.3.1: 实验任务配置管理**
    *   **描述:** 实现实验任务配置的增删改查（CRUD）功能。这些配置将预设实验所需的所有模型和算法。
    *   **`crud_experiment_task_config.py` 方法签名（示例）:**
        ```python
        from sqlalchemy.orm import Session
        from app.models import common, db_models

        def get_experiment_task_config(db: Session, task_config_id: str) -> Optional[db_models.ExperimentTaskConfiguration]: ...
        def get_experiment_task_configs(db: Session, skip: int = 0, limit: int = 100) -> List[db_models.ExperimentTaskConfiguration]: ...
        def create_experiment_task_config(db: Session, task_config: common.ExperimentTaskConfigInput) -> db_models.ExperimentTaskConfiguration: ...
        def update_experiment_task_config(db: Session, task_config_id: str, task_config_update: common.ExperimentTaskConfigInput) -> Optional[db_models.ExperimentTaskConfiguration]: ...
        def delete_experiment_task_config(db: Session, task_config_id: str) -> bool: ...
        ```
    *   **`config_manager_service.py` 方法签名（示例，在 `ConfigManagerService` 类中添加）:**
        ```python
        async def create_experiment_task_config(self, task_config_in: common.ExperimentTaskConfigInput) -> common.ExperimentTaskConfigResponse: ...
        async def get_experiment_task_config_response(self, task_config_id: str) -> Optional[common.ExperimentTaskConfigResponse]: ... # 用于API响应，会填充名称字段
        async def get_all_experiment_task_configs_response(self) -> List[common.ExperimentTaskConfigResponse]: ... # 用于API响应
        async def update_experiment_task_config(self, task_config_id: str, task_config_update: common.ExperimentTaskConfigInput) -> Optional[common.ExperimentTaskConfigResponse]: ...
        async def delete_experiment_task_config(self, task_config_id: str) -> bool: ...
        async def get_full_experiment_task_config(self, task_config_id: str) -> Optional[common.ExperimentTaskConfig]: ... # 内部使用，获取原始配置
        ```
    *   **API端点（experiment_task_configs.py）:**
        *   `POST /experiment-task-configs/`：创建（请求体: `ExperimentTaskConfigInput`，响应: `ExperimentTaskConfigResponse`）
        *   `GET /experiment-task-configs/`：获取列表（响应: `List[ExperimentTaskConfigResponse]`）
        *   `GET /experiment-task-configs/{task_config_id}`：获取单个（响应: `ExperimentTaskConfigResponse`）
        *   `PUT /experiment-task-configs/{task_config_id}`：更新（请求体: `ExperimentTaskConfigInput`，响应: `ExperimentTaskConfigResponse`）
        *   `DELETE /experiment-task-configs/{task_config_id}`：删除（响应: `common.MessageResponse`）
    *   **完成检查点:**
        *   [ ] `crud_experiment_task_config.py` 中的CRUD操作逻辑实现。
        *   [ ] 服务层方法在 `config_manager_service.py` 中实现，包括将ID解析为名称的逻辑。
        *   [ ] FastAPI端点在 `experiment_task_configs.py` 中定义完成，并通过Swagger UI测试。
        *   [ ] 实验任务配置数据能正确存入和读取数据库。

## 3. 核心服务实现

**目标:** 实现API调用服务、日志服务和结果存储服务的基础。

**Phase 3.1: `api_caller_service`**
*   **文件:** `app/services/api_caller_service.py`
*   **依赖:** `llm_api_integrator`（已完成）、`httpx`
*   **任务 3.1.1: 调用LLM API（通过 `llm_api_integrator`）**
    *   **描述:** 封装对`llm_api_integrator`的调用，处理Prompt构建（基于模板和变量）、参数传递、错误捕获。
    *   **方法签名:**
        ```python
        from app.models import common
        # from llm_api_integrator import LLMFactory, LLMInterface

        class ApiCallerService:
            def __init__(self, http_client: Optional[httpx.AsyncClient] = None):
                self.http_client = http_client or httpx.AsyncClient(timeout=60.0) # 默认超时改为60秒

            async def call_llm_api(
                self,
                provider: str, # 对应 llm_api_integrator 的 provider_name
                model_name: str,
                api_key: str,
                prompt: str,
                additional_params: Optional[Dict[str, Any]] = None
            ) -> str:
                """
                调用指定LLM（通过llm_api_integrator）。
                处理客户端实例化和响应生成。
                保证非流式返回。
                """
                # 实现已更新为使用 llm_api_integrator.LLMFactory
                # 内部调用 llm_client.async_generate_response(stream=False, **additional_params)
                # 并处理 llm_api_integrator.exceptions 下的各类错误
                pass # 详细实现见 api_caller_service.py

            async def call_llm_api_with_config_id( # 新增或修改为此方法
                self,
                llm_config_id: str,
                prompt: str,
                # db_session_for_config: Session # 如果需要从这里传入db会话
            ) -> str:
                """
                通过llm_config_id调用LLM，自动从ConfigManagerService获取密钥和配置。
                """
        ```
    *   **完成检查点:**
        *   [x] 方法实现，能根据配置动态获取llm_api_integrator客户端并调用。
        *   [x] `async_generate_response`的`stream`参数固定为`False`。
        *   [x] 基本错误处理和日志记录 (捕获 `llm_api_integrator` 的特定异常)。

*   **任务 3.1.2: 调用外部HTTP API（知识图谱/向量数据库/检索算法）**
    *   **描述:** 通用方法调用外部HTTP接口，用于与知识图谱系统（抽取原始语料、发送问答对进行检索）和待评估的相似度检索算法API交互。
    *   **方法签名:**
        ```python
        async def call_external_http_api(
            self,
            url: HttpUrl,
            method: str = "GET",
            params: Optional[Dict[str, Any]] = None, # GET请求参数
            json_data: Optional[Dict[str, Any]] = None, # POST/PUT请求体
            headers: Optional[Dict[str, str]] = None
        ) -> httpx.Response: # 返回完整响应对象
            """请求外部服务。"""
            pass # 实现略
        ```
    *   **完成检查点:**
        *   [ ] 方法用httpx实现。
        *   [ ] 支持GET/POST等方法，参数、JSON体、头部传递。
        *   [ ] 包含错误处理（超时、HTTP错误）。

**Phase 3.2: `logger_service`**
*   **文件:** `app/services/logger_service.py`
*   **任务 3.2.1: JSONL日志记录服务**
    *   **描述:** 实现将实验过程中的关键步骤和数据以JSONL格式记录到文件。每个实验运行（run）有独立日志文件。
    *   **方法签名:**
        ```python
        import json
        from datetime import datetime
        import os

        class LoggerService:
            def __init__(self, base_log_dir: str = "experiment_logs"):
                self.base_log_dir = base_log_dir
                os.makedirs(self.base_log_dir, exist_ok=True)
                self.current_run_id: Optional[str] = None
                self.current_log_file_path: Optional[str] = None

            def start_run_logging(self, run_id: str):
                self.current_run_id = run_id
                self.current_log_file_path = os.path.join(self.base_log_dir, f"run_{run_id}.jsonl")
                # 新建/清空日志文件
                with open(self.current_log_file_path, "w") as f:
                    pass

            def log_step(self, step_name: str, details: Dict[str, Any]):
                if not self.current_log_file_path:
                    return

                log_entry = {
                    "timestamp": datetime.utcnow().isoformat(),
                    "run_id": self.current_run_id,
                    "step_name": step_name,
                    "details": details
                }
                try:
                    with open(self.current_log_file_path, "a") as f:
                        f.write(json.dumps(log_entry, ensure_ascii=False) + "\\n")
                except Exception as e:
                    pass

            def get_log_file_path(self) -> Optional[str]:
                return self.current_log_file_path
        ```
    *   **完成检查点:**
        *   [ ] `start_run_logging`初始化日志文件。
        *   [ ] `log_step`追加结构化日志条目。
        *   [ ] 日志条目包含时间戳、运行ID、步骤名和详细信息。

**Phase 3.3: `result_storage_service`**
*   **文件:** `app/services/result_storage_service.py`（初期可用文件存储，后续集成数据库）
*   **任务 3.3.1: 实验结果存储服务（初步）**
    *   **描述:** 存储每次实验运行的最终报告。初期可存为JSON文件，后续再集成数据库。
    *   **方法签名:**
        ```python
        import json
        import os
        from app.models import common

        class ResultStorageService:
            def __init__(self, base_results_dir: str = "experiment_results"):
                self.base_results_dir = base_results_dir
                os.makedirs(self.base_results_dir, exist_ok=True)

            async def save_experiment_run_report(self, report: common.ExperimentRunReport):
                report_path = os.path.join(self.base_results_dir, f"report_{report.run_id}.json")
                try:
                    with open(report_path, "w") as f:
                        json.dump(report.model_dump(mode='json'), f, indent=2, ensure_ascii=False)
                except Exception as e:
                    raise
            
            async def get_experiment_run_report(self, run_id: str) -> Optional[common.ExperimentRunReport]:
                report_path = os.path.join(self.base_results_dir, f"report_{run_id}.json")
                try:
                    if os.path.exists(report_path):
                        with open(report_path, "r") as f:
                            data = json.load(f)
                            return common.ExperimentRunReport(**data)
                    return None
                except Exception as e:
                    return None

            async def get_all_run_ids(self) -> List[str]:
                ids = []
                for filename in os.listdir(self.base_results_dir):
                    if filename.startswith("report_") and filename.endswith(".json"):
                        ids.append(filename.replace("report_", "").replace(".json", ""))
                return ids
        ```
    *   **完成检查点:**
        *   [ ] `save_experiment_run_report`将报告序列化为JSON并保存。
        *   [ ] `get_experiment_run_report`能加载报告。
        *   [ ] `get_all_run_ids`能列出已保存报告。

## 4. 实验编排器（experiment_orchestrator）

**目标:** 实现核心的实验流程。
**文件:** `app/services/experiment_orchestrator_service.py`、`app/utils/objective_scorer.py`

**Phase 4.1: Objective Scorer Utility**
*   **文件:** `app/utils/objective_scorer.py`
*   **任务 4.1.1: 实现IoU计算逻辑**
    *   **描述:** 按文档定义计算IoU和分数。
        *   G: 标注ID集合
        *   R: 检索ID集合
        *   IoU = |G ∩ R| / |G ∪ R|
        *   Score = round(10 * IoU, 2)
    *   **方法签名:**
        ```python
        from typing import Set, List
        from app.models.common import IoUScoreResult

        def calculate_iou_score(retrieved_ids: List[str], ground_truth_ids: List[str]) -> IoUScoreResult:
            set_retrieved = set(retrieved_ids)
            set_ground_truth = set(ground_truth_ids)

            intersection = set_retrieved.intersection(set_ground_truth)
            union = set_retrieved.union(set_ground_truth)

            intersection_count = len(intersection)
            union_count = len(union)

            iou = intersection_count / union_count if union_count > 0 else 0.0
            score = round(10 * iou, 2)

            return IoUScoreResult(
                retrieved_ids=list(set_retrieved),
                ground_truth_ids=list(set_ground_truth),
                intersection_count=intersection_count,
                union_count=union_count,
                iou=iou,
                score=score
            )
        ```
    *   **完成检查点:**
        *   [ ] `calculate_iou_score`方法实现并单元测试。

        def calculate_objective_information_coverage_score(retrieved_ids: List[str], ground_truth_ids: List[str]) -> float:
            """
            根据需求文档计算客观信息覆盖度分数。
            Score = (|G ∩ R| / |G|) * 10
            """
            if not ground_truth_ids: # 避免除以零
                return 0.0
            
            set_retrieved = set(retrieved_ids)
            set_ground_truth = set(ground_truth_ids)
            
            intersection_count = len(set_retrieved.intersection(set_ground_truth))
            ground_truth_count = len(set_ground_truth)
            
            if ground_truth_count == 0: # 再次检查，确保安全
                 return 0.0

            coverage_ratio = intersection_count / ground_truth_count
            score = round(10 * coverage_ratio, 2)
            return score

**Phase 4.2: `ExperimentOrchestratorService`实验编排服务**
*   **文件:** `app/services/experiment_orchestrator_service.py`
*   **依赖:** `ConfigManagerService`、`ApiCallerService`、`LoggerService`、`ResultStorageService`、`objective_scorer`
*   **任务 4.2.1: 初始化与参数准备**
    *   **描述:** Orchestrator服务初始化，以及运行实验前准备必要的配置。
    *   **方法签名（主执行方法）:**
        ```python
        import uuid
        from app.models import common

        class ExperimentOrchestratorService:
            def __init__(
                self,
                config_service: ConfigManagerService,
                api_caller: ApiCallerService,
                logger: LoggerService,
                result_storage: ResultStorageService
            ):
                self.config_service = config_service
                self.api_caller = api_caller
                self.logger = logger
                self.result_storage = result_storage

            async def _prepare_prompt(self, template: str, variables: Dict[str, str]) -> str:
                prompt = template
                for key, value in variables.items():
                    prompt = prompt.replace(f"{{{{{key}}}}}", str(value))
                return prompt

            async def run_experiment_flow(
                self,
                run_id: str,
                algorithm_config_id: str,
                qa_generator_model_config_id: str,
                answer_predictor_model_config_id: str,
                evaluator_model_config_id: str,
                max_corpus_items: int = 10
            ) -> common.ExperimentRunReport:
                
                self.logger.start_run_logging(run_id)
                self.logger.log_step("ExperimentStart", {"run_id": run_id, "params": locals()})

                # 0. 加载配置
                algo_config = await self.config_service.get_retrieval_algorithm_config(algorithm_config_id)
                # 获取用于报告的配置信息（不含密钥）
                qa_config_for_report = await self.config_service.get_llm_model_config_for_response(qa_generator_model_config_id)
                ans_config_for_report = await self.config_service.get_llm_model_config_for_response(answer_predictor_model_config_id)
                eval_config_for_report = await self.config_service.get_llm_model_config_for_response(evaluator_model_config_id)

                if not all([algo_config, qa_config_for_report, ans_config_for_report, eval_config_for_report]):
                    error_msg = "One or more configurations not found."
                    self.logger.log_step("ConfigError", {"error": error_msg})
                    raise ValueError(error_msg)

                report = common.ExperimentRunReport(
                    run_id=run_id,
                    algorithm_config=algo_config,
                    qa_generator_config=qa_config_for_report,
                    answer_predictor_config=ans_config_for_report,
                    evaluator_config=eval_config_for_report,
                    raw_corpus_items=[], generated_qa_items=[],
                    all_similarity_search_results=[], all_predicted_answers=[],
                    all_evaluation_scores=[], all_iou_scores=[],
                    all_objective_information_coverage_scores=[], # 新增初始化
                    full_log_path=self.logger.get_log_file_path()
                )
                # ...后续流程...
        ```
    *   **完成检查点:**
        *   [ ] Orchestrator类结构和构造函数。
        *   [ ] `run_experiment_flow`方法骨架。
        *   [ ] 配置加载逻辑。
        *   [ ] 实验开始日志。
        *   [ ] `_prepare_prompt`辅助方法。

*   **任务 4.2.2: Step 1 - 抽取原始语料（DataFetcherRandom）**
    *   **描述:** 调用随机抽样API（根据`RetrievalAlgorithmConfig.corpus_extraction_api.endpoint`，例如 `https://knowledge.public.yzint.cn/knowledge/v1/graph/entity/random`）抽取原始语料。该过程现在包含以下逻辑：
        *   API的`count`参数固定为`"2"`，用于获取一对相关的实体及其关系。
        *   维护一个已抽取 *实体对中两个实体* 的 `id` 集合（`extracted_entity_ids`），用于在后续请求中通过 `entityIds` URL参数传递，以避免重复请求相同的实体对。
        *   API响应结构预期为（示例）：
            ```json
            {
                "code": 0,
                "success": true,
                "data": {
                    "entities": [
                        { "id": "entity_id_1", "name": "Target Entity Name", "description": "Target entity description.", "type": "target_type" },
                        { "id": "entity_id_2", "name": "Source Entity Name", "description": "Source entity description.", "type": "source_type" }
                    ],
                    "relationships": [
                        { "id": "rel_id_1", "source": "entity_id_2", "target": "entity_id_1", "relationship": "Description of the relationship." }
                    ],
                    "vectors": [
                        [ { "vector_id": "vec_t1" }, { "vector_id": "vec_t2" } ], // Vectors for entity_id_1
                        [ { "vector_id": "vec_s1" } ]  // Vectors for entity_id_2
                    ]
                }
            }
            ```
        *   **数据解析与处理** (`_fetch_entity_pair_from_corpus_api` 方法):
            *   从 `data.entities` 中获取目标实体 `entities[0]` 和源实体 `entities[1]`。
            *   从 `data.relationships[0]` 中获取关系描述文本 (`relationship_data.relationship`)。
            *   为目标实体和源实体分别创建 `CorpusItem` 对象：
                *   `CorpusItem.id` 设置为各自的 `entity.id`。
                *   `CorpusItem.content` 设置为各自的 `entity.description`。
                *   `CorpusItem.metadata` 中存储各自的 `entity.name`, `entity.type`。
                *   从对应的 `data.vectors[i]` (一个向量块列表) 中提取所有 `vector_chunk.vector_id`，并将这些 `vector_id` 组成一个列表存储在各自 `CorpusItem.metadata["all_vector_ids"]`。这些 `vector_id` 将作为后续IoU计算的真实标签。
            *   所有创建的 `CorpusItem` 存入服务实例的 `self.corpus_items_map` (以实体ID为键)。
            *   如果成功解析，返回一个包含以下键的字典：`"entity1_data"` (原始目标实体字典), `"entity2_data"` (原始源实体字典), `"relationship_text"`, `"entity1_id"`, `"entity2_id"`。
        *   `extract_corpus` 方法负责调用 `_fetch_entity_pair_from_corpus_api`，目标是获取指定数量的独立实体对。每次成功获取一对后，这对中的两个实体ID会被加入到传递给下一次API调用的 `exclude_entity_ids` 集合中。
    *   **逻辑 within `run_experiment_flow` (调用 `extract_corpus`):**
        ```python
        # ... (获取 experiment_config, actual_max_corpus_items 代表目标QA对数量) ...
            report.status = common.ExperimentRunStatus.CORPUS_EXTRACTION
        self.logger.log_step("EntityPairExtractionStart", { # ... log params ... 
            "target_pair_count": actual_max_corpus_items
        })

        extracted_entity_pairs: List[Dict[str, Any]] = await self.extract_corpus(
            experiment_config=experiment_config, 
            num_target_pairs=actual_max_corpus_items
        )
        
        # 将 self.corpus_items_map 中的所有 CorpusItem 填充到报告中，供参考
        report.raw_corpus_items = list(self.corpus_items_map.values())
        
        # ... (日志记录 extracted_entity_pairs 的数量和预览) ...
        if not extracted_entity_pairs and actual_max_corpus_items > 0:
            # ... (处理未获取到任何实体对的情况) ...
        ```
    *   **完成检查点:**
        *   [x] `_fetch_entity_pair_from_corpus_api` 实现API调用（`count=2`固定），解析响应中的两个实体、关系和向量。
        *   [x] 为每个实体创建`CorpusItem`（包含`all_vector_ids`）并存入`self.corpus_items_map`。
        *   [x] `_fetch_entity_pair_from_corpus_api` 返回包含实体对数据和关系文本的字典。
        *   [x] `extract_corpus` 调用 `_fetch_entity_pair_from_corpus_api` 获取指定数量的实体对，并使用 `exclude_entity_ids` 进行去重。
        *   [x] `run_experiment_flow` 正确调用 `extract_corpus`。
        *   [x] `report.raw_corpus_items` 由 `self.corpus_items_map.values()` 填充。
        *   [x] 日志记录和状态更新。

*   **任务 4.2.3: Step 2 - 大模型1生成问答对（QAItemGenerator）**
    *   描述: 对每个抽取的实体对数据（包含目标实体、源实体和关系描述），使用LLM1生成一个问答对。 
    *   **逻辑 (主要在 `_generate_qa_for_item` 中，由 `generate_qa_pairs` 调用):**
        ```python
        # item_for_qa 是从 extract_corpus 接收到的实体对字典:
        # { "entity1_data": ..., "entity2_data": ..., "relationship_text": ..., "entity1_id": ..., "entity2_id": ... }

        # 1. 构造 original_corpus_content:
        entity1 = item_for_qa["entity1_data"] # 目标实体
        entity2 = item_for_qa["entity2_data"] # 源实体
        relationship_description = item_for_qa["relationship_text"]
        target_entity_name = entity1.get("name", "N/A")
        target_entity_desc = entity1.get("description", "N/A")
        source_entity_name = entity2.get("name", "N/A")
        source_entity_desc = entity2.get("description", "N/A")
        
        original_corpus_content_str = (
            f"目标实体 (Target):\n"
            f"  Name: \"{target_entity_name}\"\n"
            f"  Description: \"{target_entity_desc}\"\n\n"
            f"关联实体 (Source):\n"
            f"  Name: \"{source_entity_name}\"\n"
            f"  Description: \"{source_entity_desc}\"\n\n"
            f"关系:\n"
            f"  目标实体 '{target_entity_name}' 与关联实体 '{source_entity_name}' 的关系是: {relationship_description}"
        )

        # 2. 准备 QAItem 字段:
        original_corpus_item_id_str = f"{item_for_qa['entity1_id']},{item_for_qa['entity2_id']}"
        # 假设问题主要关于源实体 (entity2 / source entity)
        question_target_entity_id = item_for_qa['entity2_id'] 

        # 3. 调用LLM1进行QA生成 (使用 qa_prompt = prompt_template.format(context=original_corpus_content_str))
        # ... (调用 self.api_caller.call_llm_api with llm_config_id, prompt) ...
        # ... (解析LLM响应, extract_json_from_markdown) ...

        # 4. 创建 GeneratedQAItem:
        qa_item = common.GeneratedQAItem(
            original_corpus_item_id=original_corpus_item_id_str,
            original_corpus_content=original_corpus_content_str,
                    question=str(parsed_qa["question"]),
            generated_answer=str(parsed_qa["answer"]),
            question_entity_id=question_target_entity_id 
                )
                report.generated_qa_items.append(qa_item)
        # ... (日志记录) ...
        ```
    *   **完成检查点:**
        *   [x] `_generate_qa_for_item` 接收实体对字典作为输入。
        *   [x] 正确构造包含目标实体、源实体和它们之间关系描述的 `original_corpus_content` 字符串。
        *   [x] `GeneratedQAItem.original_corpus_item_id` 设置为逗号分隔的两个实体ID。
        *   [x] `GeneratedQAItem.question_entity_id` 设置为问题主要关联的实体ID（例如源实体ID）。
        *   [x] 调用LLM1并解析响应，填充 `report.generated_qa_items`。
        *   [x] 日志记录和状态更新。

*   **任务 4.2.4: Step 3 - 相似度检索（DataFetcherSimilarity）及IoU计算**
    *   **描述:** 对每个生成的问题 (`QAItem`)，调用配置的相似度检索API（例如`/api/select_chunks`），执行两阶段检索。
        1.  **Greedy阶段检索:** 
            *   调用API，参数包括 `query_text` (来自`qa_item.question`)，`selection_method="greedy"`。 `top_n_candidates` 可从 `similarity_search_api.body` 中配置。
            *   API响应预期包含 `selected_chunks: List[Dict]` 和 `k: int`。每个 `chunk` 中的 `id` 字段被视为 `vector_id`，`description` 字段优先用于内容。
            *   解析 `selected_chunks`，转换为 `List[SearchResultItem]` (其中 `SearchResultItem.id` 是 `vector_id`, `SearchResultItem.content` 优先使用 `chunk.description`, `score`设为None)。
            *   创建 `SimilaritySearchResult` 对象，标记 `selection_method="greedy"`，并存储API返回的 `k` 值到 `k_value` 字段。
            *   **IoU 计算**: `ground_truth_ids` 使用与 `qa_item.question_entity_id` (现在是源实体ID) 关联的 `CorpusItem` 的 `metadata["all_vector_ids"]` (从 `self.corpus_items_map` 中获取)。
        2.  **Simple阶段检索 (仅当Greedy阶段成功返回 `k > 0` 时执行):**
            *   调用API，参数包括 `query_text` (来自`qa_item.question`)，`selection_method="simple"`，以及从Greedy阶段获取的 `k` 值。`top_n_candidates` 仍然适用。
            *   API响应预期包含 `selected_chunks: List[Dict]`。解析方式同Greedy阶段。
            *   创建 `SimilaritySearchResult` 对象，标记 `selection_method="simple"`，并存储使用的 `k` 值到 `k_value` 字段。
            *   **IoU 计算**: 同Greedy阶段。
    *   **逻辑 within `run_experiment_flow`:**
        ```python
        # ... (初始化 report.all_similarity_search_results 等列表) ...
        similarity_api_conf = algo_config.similarity_search_api
        DEFAULT_TOP_N_CANDIDATES = 3000

        for qa_item in report.generated_qa_items:
            # 定位与 qa_item 关联的原始 CorpusItem
            original_corpus_item_for_qa = next((ci for ci in report.raw_corpus_items if ci.id == qa_item.original_corpus_item_id), None)
            ground_truth_vector_ids = []
            if original_corpus_item_for_qa and original_corpus_item_for_qa.metadata:
                ground_truth_vector_ids = original_corpus_item_for_qa.metadata.get("all_vector_ids", [])
            if not ground_truth_vector_ids:
                self.logger.log_step("GroundTruthWarning", {"original_corpus_id": qa_item.original_corpus_item_id, "message": "未能找到原始语料的vector_id列表用于IoU。"})

            # --- Greedy 检索阶段 ---
            # ... (构造 greedy_payload) ...
            try:
                # ... (调用API, 解析 greedy_data, selected_chunks_greedy, k_from_greedy) ...
                greedy_search_result_items = []
                for res_item in selected_chunks_greedy: # res_item['id'] is vector_id
                    # ... (创建 SearchResultItem) ...
                
                greedy_similarity_result = common.SimilaritySearchResult(
                    question_item=qa_item,
                    retrieved_items=greedy_search_result_items,
                    ground_truth_ids=ground_truth_vector_ids, # 使用上面获取的 all_vector_ids
                    selection_method="greedy",
                    k_value=k_from_greedy # 新增：存储Greedy返回的k
                )
                report.all_similarity_search_results.append(greedy_similarity_result)

                # IoU 和客观覆盖度计算 for Greedy
                greedy_retrieved_vector_ids = [item.id for item in greedy_search_result_items if item.id]
                iou_greedy = calculate_iou_score(greedy_retrieved_vector_ids, ground_truth_vector_ids)
                report.all_iou_scores.append(iou_greedy)
                # ... (类似地计算和记录 obj_cov_greedy_details) ...
            except Exception as e_greedy:
                # ... (log error) ...

            # --- Simple 检索阶段 (if k_from_greedy is not None) ---
            # ... (构造 simple_payload with k_from_greedy) ...
            try:
                # ... (调用API, 解析 simple_data, selected_chunks_simple) ...
                simple_search_result_items = []
                for res_item in selected_chunks_simple: # res_item['id'] is vector_id
                    # ... (创建 SearchResultItem) ...

                simple_similarity_result = common.SimilaritySearchResult(
                    question_item=qa_item,
                    retrieved_items=simple_search_result_items,
                    ground_truth_ids=ground_truth_vector_ids, # 与Greedy阶段的GT相同
                    selection_method="simple",
                    k_value=k_from_greedy # 新增：存储Simple阶段使用的k
                )
                report.all_similarity_search_results.append(simple_similarity_result)

                # IoU 和客观覆盖度计算 for Simple
                simple_retrieved_vector_ids = [item.id for item in simple_search_result_items if item.id]
                iou_simple = calculate_iou_score(simple_retrieved_vector_ids, ground_truth_vector_ids)
                report.all_iou_scores.append(iou_simple)
                # ... (类似地计算和记录 obj_cov_simple_details) ...
            except Exception as e_simple:
                # ... (log error) ...
        # ... (结束循环和日志) ...
        ```
    *   **完成检查点:**
-        *   [x] 遍历QA项。
-        *   [x] **Greedy阶段:** 正确构造请求体（`query_text`, `selection_method="greedy"`, `top_n_candidates` from config/default），调用API，解析 `selected_chunks` 和 `k`。
-        *   [x] **Simple阶段:** 正确构造请求体（`query_text`, `selection_method="simple"`, `top_n_candidates`, `k` from greedy），调用API，解析 `selected_chunks`。
-        *   [x] `selected_chunks` 中的每个元素 (e.g. `{"id": ..., "description": ..., "vector": ...}`) 被正确转换为 `SearchResultItem` ( `id` -> `id`, `description` (优先) / `content` / `text` -> `content`, `score` is `None`, `vector`被忽略)。
-        *   [x] 为Greedy和Simple的每次成功检索创建 `SimilaritySearchResult` 对象，标记 `selection_method`，存储 `k_value`，并添加到 `report.all_similarity_search_results`。
-        *   [x] IoU和客观覆盖度计算并填充报告，日志记录，状态更新 (分别为Greedy和Simple的结果进行)。

*   **任务 4.2.5: Step 4 - 大模型2预测答案（AnswerGenerator）**
    *   **描述:** 对每个(问题, 检索结果)，用LLM2预测答案。
    *   **逻辑 within `run_experiment_flow`:**
        ```python
        for search_res in report.all_similarity_search_results:
            try:
                retrieved_context_str = "\n\n".join(
                    [f"Document ID: {item.id}\nContent: {item.content}" for item in search_res.retrieved_items if item.content]
                )
                if not retrieved_context_str:
                    retrieved_context_str = "No relevant documents found."

                prompt_vars = {
                    "question": search_res.question_item.question,
                    "retrieved_context": retrieved_context_str
                }
                # ans_config 对象不再直接可用
                current_ans_config = await self.config_service.get_llm_model_config(answer_predictor_model_config_id)
                if not current_ans_config: raise ValueError("Answer Predictor config not found")
                ans_predict_prompt = await self._prepare_prompt(current_ans_config.prompt_template, prompt_vars)
                
                predicted_ans_text = await self.api_caller.call_llm_api_with_config_id( # 使用新方法
                    llm_config_id=answer_predictor_model_config_id,
                    prompt=ans_predict_prompt
                )
                
                predicted_answer_obj = common.PredictedAnswer(
                    similarity_search_result=search_res,
                    predicted_answer_text=predicted_ans_text
                )
                report.all_predicted_answers.append(predicted_answer_obj)
                self.logger.log_step("AnswerPrediction", {"question": search_res.question_item.question, "predicted_answer_obj": predicted_answer_obj.model_dump()})
            except Exception as e:
                self.logger.log_step("AnswerPredictionError", {"question": search_res.question_item.question, "error": str(e)})
        ```
    *   **完成检查点:**
        *   [ ] 遍历检索结果。
        *   [ ] 检索内容拼接。
        *   [ ] LLM2 prompt准备及调用。
        *   [ ] 填充`report.all_predicted_answers`，日志记录。

*   **任务 4.2.6: Step 5 - 大模型3评估打分（Evaluator）**
    *   **描述:** 用LLM3对(原始语料, 问答对, 预测答案)进行三维度主观评估。
    *   **逻辑 within `run_experiment_flow`:**
        ```python
        for pred_answer_obj in report.all_predicted_answers:
            try:
                current_qa_item = pred_answer_obj.similarity_search_result.question_item
                
                # 生成检索上下文摘要/内容
                retrieved_items_for_eval = pred_answer_obj.similarity_search_result.retrieved_items
                retrieved_context_str_for_eval = "\n\n".join(
                    [f"Retrieved Document ID: {item.id}\nContent: {item.content}" 
                     for item in retrieved_items_for_eval if item.content]
                )
                if not retrieved_context_str_for_eval:
                    retrieved_context_str_for_eval = "No relevant documents were retrieved by the search algorithm."

                prompt_vars = {
                    "original_context": current_qa_item.original_corpus_content,
                    "question": current_qa_item.question,
                    "generated_answer_llm1": current_qa_item.generated_answer, # 这是LLM1生成的参考答案
                    "retrieved_context_summary": retrieved_context_str_for_eval, # 使用上面生成的字符串
                    "predicted_answer_llm2": pred_answer_obj.predicted_answer_text
                }
                # eval_config 对象不再直接可用
                current_eval_config = await self.config_service.get_llm_model_config(evaluator_model_config_id)
                if not current_eval_config: raise ValueError("Evaluator config not found")
                eval_prompt = await self._prepare_prompt(current_eval_config.prompt_template, prompt_vars)

                raw_eval_response_str = await self.api_caller.call_llm_api_with_config_id( # 使用新方法
                    llm_config_id=evaluator_model_config_id,
                    prompt=eval_prompt
                )
                parsed_eval_scores = json.loads(raw_eval_response_str)

                evaluation_score = common.EvaluationScores(
                    context_relevance=float(parsed_eval_scores.get("context_relevance", 0.0)),
                    information_coverage=float(parsed_eval_scores.get("information_coverage", 0.0)),
                    answer_accuracy=float(parsed_eval_scores.get("answer_accuracy", 0.0))
                )
                report.all_evaluation_scores.append(evaluation_score)
                self.logger.log_step("EvaluationScoring", {"question": current_qa_item.question, "scores": evaluation_score.model_dump()})
            except Exception as e:
                self.logger.log_step("EvaluationScoringError", {"question": current_qa_item.question if 'current_qa_item' in locals() else "Unknown", "error": str(e)})
        ```
    *   **完成检查点:**
        *   [ ] 收集LLM3输入。
        *   [ ] prompt准备及调用。
        *   [ ] 解析LLM3响应。
        *   [ ] 填充`report.all_evaluation_scores`，日志记录。

*   **任务 4.2.7: 汇总报告并保存**
    *   **描述:** 计算平均分，保存报告。
    *   **逻辑 within `run_experiment_flow`:**
        ```python
        if report.all_iou_scores:
            report.overall_avg_iou_score = sum(s.score for s in report.all_iou_scores) / len(report.all_iou_scores)
        
        # 新增：计算客观信息覆盖度平均分
        if report.all_objective_information_coverage_scores:
            report.overall_avg_objective_information_coverage_score = sum(s.score for s in report.all_objective_information_coverage_scores) / len(report.all_objective_information_coverage_scores)

        if report.all_evaluation_scores: # LLM3主观评分的平均值
            report.overall_avg_context_relevance = sum(s.context_relevance for s in report.all_evaluation_scores) / len(report.all_evaluation_scores)
            report.overall_avg_information_coverage = sum(s.information_coverage for s in report.all_evaluation_scores) / len(report.all_evaluation_scores) # LLM3的主观覆盖度
            report.overall_avg_answer_accuracy = sum(s.answer_accuracy for s in report.all_evaluation_scores) / len(report.all_evaluation_scores)

        self.logger.log_step("ExperimentEnd", {"run_id": run_id, "final_report_summary": {
            "avg_iou_score": report.overall_avg_iou_score, 
            "avg_objective_information_coverage_score": report.overall_avg_objective_information_coverage_score, # 新增客观覆盖度均值
            "avg_llm_context_relevance": report.overall_avg_context_relevance, # LLM3主观相关性均值
            "avg_llm_information_coverage": report.overall_avg_information_coverage, # LLM3主观覆盖度均值
            "avg_llm_answer_accuracy": report.overall_avg_answer_accuracy # LLM3主观准确度均值
        }})
        
        await self.result_storage.save_experiment_run_report(report)
        return report
        ```
    *   **完成检查点:**
        *   [ ] 计算均值。
        *   [ ] 保存完整报告。
        *   [ ] 返回最终报告。

*   **任务 4.2.8: 实验执行API端点**
    *   **文件:** `app/apis/v1/endpoints/experiments_api.py`
    *   **描述:** 创建API端点触发`run_experiment_flow`。
    *   **请求体模型:**
        ```python
        class ExperimentRunRequest(BaseModel):
            algorithm_config_id: str
            qa_generator_model_config_id: str
            answer_predictor_model_config_id: str
            evaluator_model_config_id: str
            max_corpus_items: Optional[int] = 10
            custom_run_id: Optional[str] = None
        ```
    *   **端点:**
        *   `POST /experiments/run`（请求体: `ExperimentRunRequest`，响应: `ExperimentRunReport` 或 `task_id`）
        ```python
        @router.post("/run", response_model=common.ExperimentRunReport)
        async def run_experiment_endpoint(
            request_data: ExperimentRunRequest,
            orchestrator_service: ExperimentOrchestratorService = Depends(get_orchestrator_service)
        ):
            run_id = request_data.custom_run_id or str(uuid.uuid4())
            try:
                report = await orchestrator_service.run_experiment_flow(
                    run_id=run_id,
                    algorithm_config_id=request_data.algorithm_config_id,
                    qa_generator_model_config_id=request_data.qa_generator_model_config_id,
                    answer_predictor_model_config_id=request_data.answer_predictor_model_config_id,
                    evaluator_model_config_id=request_data.evaluator_model_config_id,
                    max_corpus_items=request_data.max_corpus_items
                )
                return report
            except ValueError as ve:
                raise HTTPException(status_code=404, detail=str(ve))
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"Experiment failed: {str(e)}")
        ```
    *   **完成检查点:**
        *   [ ] `ExperimentRunRequest`模型定义。
        *   [ ] API端点创建，参数映射到编排器服务。
        *   [ ] 端点返回报告（或异步任务ID）。
        *   [x] 新增任务管理端点：
            *   `GET /experiments/tasks/{task_id}/status` (响应: `TaskStatusResponse`): 查询任务状态和结果/错误。
            *   `POST /tasks/{task_id}/stop` (响应: `TaskRevokeResponse`): 尝试撤销任务。

## 5. 异步任务管理（task_manager）

**目标:** 将实验执行卸载到后台任务（如Celery）。

**已完成步骤 (Celery 基础设置):**
5.1.  **依赖更新:** 已将 `celery[redis]==5.3.6` 添加到 `backend/requirements.txt`。
5.2.  **配置添加:** 已在 `backend/app/core/config.py` 的 `Settings` 类中添加 `CELERY_BROKER_URL` 和 `CELERY_RESULT_BACKEND` (默认为 "redis://localhost:6379/0")。
5.3.  **Celery 应用创建:** 已创建 `backend/app/core/celery_worker.py` 文件，其中定义了 `celery_app = Celery(...)` 实例。
    *   配置了 broker 和 backend URL。
    *   设置 `include=["app.tasks"]` 以便自动发现任务。
    *   设置了默认序列化器为 JSON，时区为 "Asia/Shanghai"。

**后续步骤:**
*   **Celery:**
    *   [x] 配置Celery及消息队列。 (基础配置已完成)
    *   [x] 定义Celery任务包装`run_experiment_flow`。
        *   已创建 `backend/app/tasks.py`。
        *   已定义 `run_experiment_task(@celery_app.task)`，该任务调用 `ExperimentOrchestratorService.run_experiment_flow`。
        *   已在 `backend/app/services/experiment_orchestrator_service.py` 中添加 `get_experiment_orchestrator_service_for_task` 工厂函数，用于在Celery任务上下文中正确实例化服务及其依赖（包括数据库会话和`ConfigManagerService`，后者用于获取API密钥等）。
    *   [x] 修改`POST /experiments/run`提交任务并返回`task_id`。
        *   端点更名为 `run_experiment_endpoint_async`。
        *   现在调用 `run_experiment_task.delay()` 提交Celery任务。参数中不再包含API密钥。
        *   返回 `ExperimentTaskSubmissionResponse` (包含 `task_id`, `run_id`, `status_url` 形如 `"/api/v1/experiments/tasks/{task_id}/status"`)，状态码 `202 Accepted`。
    *   [x] 新增任务管理端点：
        *   `GET /experiments/tasks/{task_id}/status` (响应: `TaskStatusResponse`): 查询任务状态和结果/错误。
        *   `POST /tasks/{task_id}/stop` (响应: `TaskRevokeResponse`): 尝试撤销任务。

## 6. 结果与日志API

**目标:** 提供API访问已存结果和日志。
*   **文件:** `app/apis/v1/endpoints/experiments_api.py`
*   **任务 6.1: 获取实验报告**
    *   `GET /experiments/reports/{run_id}`（响应: `ExperimentRunReport`）
*   **任务 6.2: 列出所有实验run_id**
    *   `GET /experiments/reports/`（响应: `List[str]`）
*   **任务 6.3: 下载日志文件**
    *   `GET /experiments/logs/{run_id}`（响应: `FileResponse`）
*   **完成检查点:**
    *   [ ] 所有结果/日志检索端点实现并测试。

## 7. 前端开发（高层规划）

参见《相似度检索算法评估平台.md》中的"三、前端 (Frontend) 构建"。
前端将调用上述后端API。
关键页面：模型管理、算法配置、执行设置、结果可视化、结果数据。

## 8. 集成、测试与部署

*   替换所有mock为真实外部服务集成。
*   端到端测试全流程。
*   编写关键组件单元和集成测试。
*   部署准备（Docker化、环境配置）。

本WORKFLOW文档为开发提供详细可执行的计划。每步均有输入、输出（动作或数据模型）和完成检查点。方法签名已指定，便于开发和模块间一致性。注意根据实际LLM响应和外部API格式调整解析逻辑。
