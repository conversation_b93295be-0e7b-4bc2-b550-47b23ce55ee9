from datetime import datetime
from typing import Optional
import pytz

def convert_utc_to_local(utc_dt: datetime, target_timezone_str: str = "Asia/Shanghai") -> datetime:
    '''
    将UTC时间转换为指定时区的本地时间。

    参数:
        utc_dt: UTC datetime 对象。
        target_timezone_str: 目标时区的字符串表示 (例如 "Asia/Shanghai", "America/New_York")。

    返回:
        本地化的 datetime 对象。
    '''
    if not isinstance(utc_dt, datetime):
        raise TypeError("utc_dt 必须是 datetime 类型")

    if utc_dt.tzinfo is None or utc_dt.tzinfo.utcoffset(utc_dt) is None:
        utc_dt = pytz.utc.localize(utc_dt) # 将 naive datetime 视为 UTC
    else:
        utc_dt = utc_dt.astimezone(pytz.utc) # 将带有时区的 datetime 转换为 UTC
    
    try:
        target_timezone = pytz.timezone(target_timezone_str)
    except pytz.exceptions.UnknownTimeZoneError:
        # 如果时区无效，则退回到 UTC 并记录警告或错误，或者抛出异常
        # print(f"警告: 未知的时区 '{target_timezone_str}'. 返回UTC时间.")
        # return utc_dt # 或者 raise ValueError(f"未知的时区: {target_timezone_str}")
        raise ValueError(f"未知的时区: {target_timezone_str}")
        
    local_dt = utc_dt.astimezone(target_timezone)
    return local_dt

def format_datetime_for_response(dt: Optional[datetime], target_timezone_str: str = "Asia/Shanghai") -> Optional[str]:
    '''
    将datetime对象（可能是UTC）转换为指定时区的ISO 8601格式化字符串。
    如果输入为 None，则返回 None。
    '''
    if dt is None:
        return None
    
    try:
        local_dt = convert_utc_to_local(dt, target_timezone_str)
        return local_dt.isoformat()
    except Exception as e:
        # 处理转换或格式化过程中的潜在错误，例如无效的datetime对象
        # print(f"格式化日期时间时出错: {e}. 返回原始时间的ISO格式 (如果可能).")
        if isinstance(dt, datetime):
            return dt.isoformat() # 提供一个回退的格式化
        return str(dt) # 最差情况，返回字符串表示 