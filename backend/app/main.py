import logging
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.core.config import settings
# 确保 SessionLocal 可用，如果您的 get_db 或健康检查需要直接使用它
from app.core.database import Base, engine, SessionLocal 
from app.apis.v1 import api_router
from .mock_apis import mock_router

# -- 开始日志配置 --
# 获取根日志记录器或特定模块的日志记录器
# 配置 app 命名空间下的所有日志记录器
app_logger = logging.getLogger('app') 
app_logger.setLevel(logging.INFO) # 确保此日志记录器处理INFO及以上级别的消息

# 如果没有为 app_logger 配置处理器，则添加一个控制台处理器
if not app_logger.hasHandlers():
    console_handler = logging.StreamHandler() # 输出到 sys.stderr
    console_handler.setLevel(logging.INFO) # 此处理器处理INFO及以上级别的消息
    
    # 创建一个格式化器并将其添加到处理器
    # 格式可以根据需要调整，例如：'%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s (%(filename)s:%(lineno)d)')
    console_handler.setFormatter(formatter)
    
    app_logger.addHandler(console_handler)
    
    # 可选: 防止日志消息传播到根日志记录器，以避免重复输出（如果根日志记录器也有类似的控制台处理器）
    # app_logger.propagate = False 
# -- 结束日志配置 --

@asynccontextmanager
async def lifespan(app: FastAPI):
    app_logger.info("Application lifespan: Startup sequence initiated.")
    try:
        app_logger.info("Creating database tables if they do not exist...")
        # 注意：Base.metadata.create_all 是一个同步调用。
        # 在异步上下文中，应该小心处理阻塞调用。
        # 对于 SQLite 和简单的设置，这可能在初始启动期间是可以接受的。
        # 对于生产系统或更重的数据库，考虑离线迁移。
        Base.metadata.create_all(bind=engine)
        app_logger.info("Database tables check/creation complete.")
    except Exception as e:
        app_logger.error(f"Error during database table creation in lifespan startup: {e}", exc_info=True)
        # 根据错误，您可能想将其提升以防止应用程序启动
    
    app_logger.info("Application startup tasks complete. Yielding control to Uvicorn.")
    yield
    app_logger.info("Application lifespan: Shutdown sequence initiated.")
    # 添加任何应用程序清理代码，如果需要
    app_logger.info("Application shutdown complete.")

# 在lifespan定义之后创建app实例
app = FastAPI(
    title=settings.PROJECT_NAME,
    description="用于评估不同相似度检索算法效果的实验平台后端服务",
    version="0.1.0",
    lifespan=lifespan  # 生命周期管理
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该设置具体的源
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(mock_router) 
app.include_router(api_router, prefix=settings.API_V1_STR)


@app.get("/")
async def read_root():
    return {"message": "欢迎来到相似度检索算法实验平台后端"}

@app.get("/ping")
async def pong():
    return {"ping": "pong!"}

# 添加一个健康检查端点
@app.get("/health")
async def health_check():
    db_status = "unknown"
    db_error_details = None
    try:
        # 尝试建立一个会话并执行一个简单的查询
        db = SessionLocal()
        db.execute("SELECT 1")
        db.close()
        db_status = "connected"
    except Exception as e:
        db_status = "disconnected"
        db_error_details = f"{type(e).__name__}: {str(e)}"
        app_logger.error(f"Health check DB connection error: {db_error_details}", exc_info=True)

    return {
        "status": "healthy",
        "project_name": settings.PROJECT_NAME,
        "project_version": settings.PROJECT_VERSION,
        "database_status": db_status,
        "database_error": db_error_details if db_error_details else "No error"
    }

# TODO: 引入API路由 