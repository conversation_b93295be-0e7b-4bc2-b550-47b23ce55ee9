from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from app.models import db_models, common
from app.models.db_models import generate_uuid # 确保导入
from datetime import datetime

def create_experiment_task_config(db: Session, task_config_in: common.ExperimentTaskConfigInput) -> Optional[db_models.ExperimentTaskConfiguration]:
    """创建新的实验任务配置"""
    # 检查关联的配置是否存在
    llm_qa_config = db.query(db_models.LLMConfiguration).filter(db_models.LLMConfiguration.id == task_config_in.qa_generator_model_config_id).first()
    if not llm_qa_config:
        # 可以抛出异常或返回None，具体取决于错误处理策略
        # raise ValueError(f"QA Generator LLM Config with id {task_config_in.qa_generator_model_config_id} not found")
        return None 
    
    llm_ans_config = db.query(db_models.LLMConfiguration).filter(db_models.LLMConfiguration.id == task_config_in.answer_predictor_model_config_id).first()
    if not llm_ans_config:
        return None

    llm_eval_config = db.query(db_models.LLMConfiguration).filter(db_models.LLMConfiguration.id == task_config_in.evaluator_model_config_id).first()
    if not llm_eval_config:
        return None

    algo_config = db.query(db_models.RetrievalAlgorithmConfiguration).filter(db_models.RetrievalAlgorithmConfiguration.id == task_config_in.algorithm_config_id).first()
    if not algo_config:
        return None

    db_task_config = db_models.ExperimentTaskConfiguration(
        id=generate_uuid(),  # 生成 UUID
        name=task_config_in.name,
        description=task_config_in.description,
        qa_generator_model_config_id=task_config_in.qa_generator_model_config_id,
        answer_predictor_model_config_id=task_config_in.answer_predictor_model_config_id,
        evaluator_model_config_id=task_config_in.evaluator_model_config_id,
        algorithm_config_id=task_config_in.algorithm_config_id,
        default_max_corpus_items=task_config_in.default_max_corpus_items,
        created_at=datetime.utcnow(), # 明确设置创建时间
        updated_at=datetime.utcnow()  # 明确设置更新时间
    )
    try:
        db.add(db_task_config)
        db.commit()
        db.refresh(db_task_config)
        return db_task_config
    except IntegrityError: # 捕获例如唯一性约束冲突 (name)
        db.rollback()
        return None # 或抛出特定异常
    except Exception:
        db.rollback()
        raise

def get_experiment_task_config(db: Session, task_config_id: str) -> Optional[db_models.ExperimentTaskConfiguration]:
    """通过ID获取单个实验任务配置"""
    return db.query(db_models.ExperimentTaskConfiguration).filter(db_models.ExperimentTaskConfiguration.id == task_config_id).first()

def get_experiment_task_configs(db: Session, skip: int = 0, limit: int = 100) -> List[db_models.ExperimentTaskConfiguration]:
    """获取实验任务配置列表（分页）"""
    return db.query(db_models.ExperimentTaskConfiguration).offset(skip).limit(limit).all()

def update_experiment_task_config(
    db: Session, 
    task_config_id: str, 
    task_config_update: common.ExperimentTaskConfigInput
) -> Optional[db_models.ExperimentTaskConfiguration]:
    """更新实验任务配置"""
    db_task_config = get_experiment_task_config(db, task_config_id)
    if not db_task_config:
        return None

    # 检查关联的配置是否存在 (如果ID被修改)
    if task_config_update.qa_generator_model_config_id != db_task_config.qa_generator_model_config_id and \
       not db.query(db_models.LLMConfiguration).filter(db_models.LLMConfiguration.id == task_config_update.qa_generator_model_config_id).first():
        return None # Or raise
    if task_config_update.answer_predictor_model_config_id != db_task_config.answer_predictor_model_config_id and \
       not db.query(db_models.LLMConfiguration).filter(db_models.LLMConfiguration.id == task_config_update.answer_predictor_model_config_id).first():
        return None
    if task_config_update.evaluator_model_config_id != db_task_config.evaluator_model_config_id and \
       not db.query(db_models.LLMConfiguration).filter(db_models.LLMConfiguration.id == task_config_update.evaluator_model_config_id).first():
        return None
    if task_config_update.algorithm_config_id != db_task_config.algorithm_config_id and \
       not db.query(db_models.RetrievalAlgorithmConfiguration).filter(db_models.RetrievalAlgorithmConfiguration.id == task_config_update.algorithm_config_id).first():
        return None

    update_data = task_config_update.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_task_config, key, value)
    
    db_task_config.updated_at = datetime.utcnow() # 手动更新 updated_at

    try:
        db.add(db_task_config) # SQLAlchemy 会处理是 INSERT还是UPDATE
        db.commit()
        db.refresh(db_task_config)
        return db_task_config
    except IntegrityError:
        db.rollback()
        return None
    except Exception:
        db.rollback()
        raise

def delete_experiment_task_config(db: Session, task_config_id: str) -> bool:
    """删除实验任务配置"""
    db_task_config = get_experiment_task_config(db, task_config_id)
    if not db_task_config:
        return False
    
    # 原先检查依赖的逻辑已移除
    # num_dependent_runs = db.query(db_models.ExperimentRun).filter(db_models.ExperimentRun.experiment_task_config_id == task_config_id).count()
    # if num_dependent_runs > 0:
    #     return False

    try:
        db.delete(db_task_config)
        db.commit()
        return True
    except Exception:
        db.rollback()
        # 可以记录日志或抛出更具体的异常
        return False

def get_experiment_task_config_with_names(db: Session, task_config_id: str) -> Optional[dict]:
    """
    获取单个实验任务配置，并附带关联的LLM和算法的名称。
    返回一个字典，可用于构建 ExperimentTaskConfigResponse。
    """
    result = db.query(db_models.ExperimentTaskConfiguration).filter(db_models.ExperimentTaskConfiguration.id == task_config_id).first()
    if not result:
        return None

    # 分别获取名称
    qa_gen_name = db.query(db_models.LLMConfiguration.name).filter(db_models.LLMConfiguration.id == result.qa_generator_model_config_id).scalar()
    ans_pred_name = db.query(db_models.LLMConfiguration.name).filter(db_models.LLMConfiguration.id == result.answer_predictor_model_config_id).scalar()
    eval_name = db.query(db_models.LLMConfiguration.name).filter(db_models.LLMConfiguration.id == result.evaluator_model_config_id).scalar()
    algo_name = db.query(db_models.RetrievalAlgorithmConfiguration.name).filter(db_models.RetrievalAlgorithmConfiguration.id == result.algorithm_config_id).scalar()

    try:
        task_config_validated = common.ExperimentTaskConfig.model_validate(result)
        task_config_data = task_config_validated.model_dump()
    except Exception as e: # Catches Pydantic ValidationError or other issues
        # Log the error for debugging
        # logger.error(f"Error validating/dumping ExperimentTaskConfig for ID {result.id}: {e}", exc_info=True)
        return None # Indicate failure to construct the dictionary

    task_config_data["qa_generator_model_name"] = qa_gen_name
    task_config_data["answer_predictor_model_name"] = ans_pred_name
    task_config_data["evaluator_model_name"] = eval_name
    task_config_data["algorithm_name"] = algo_name
    
    return task_config_data

def get_experiment_task_configs_with_names(db: Session, skip: int = 0, limit: int = 100) -> List[dict]:
    """
    获取实验任务配置列表，并附带关联的LLM和算法的名称。
    返回字典列表，可用于构建 ExperimentTaskConfigResponse 列表。
    """
    results = db.query(db_models.ExperimentTaskConfiguration).offset(skip).limit(limit).all()
    
    response_list = []
    for result in results:
        qa_gen_name = db.query(db_models.LLMConfiguration.name).filter(db_models.LLMConfiguration.id == result.qa_generator_model_config_id).scalar()
        ans_pred_name = db.query(db_models.LLMConfiguration.name).filter(db_models.LLMConfiguration.id == result.answer_predictor_model_config_id).scalar()
        eval_name = db.query(db_models.LLMConfiguration.name).filter(db_models.LLMConfiguration.id == result.evaluator_model_config_id).scalar()
        algo_name = db.query(db_models.RetrievalAlgorithmConfiguration.name).filter(db_models.RetrievalAlgorithmConfiguration.id == result.algorithm_config_id).scalar()

        task_config_data = common.ExperimentTaskConfig.model_validate(result).model_dump()
        task_config_data["qa_generator_model_name"] = qa_gen_name
        task_config_data["answer_predictor_model_name"] = ans_pred_name
        task_config_data["evaluator_model_name"] = eval_name
        task_config_data["algorithm_name"] = algo_name
        response_list.append(task_config_data)
        
    return response_list 