from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
import uuid

from app.models import common
from app.models.db_models import LLMConfiguration
from app.core.security import hash_api_key

class CRUDLLMConfig:
    def create(self, db: Session, *, config_in: common.LLMModelConfigInput) -> LLMConfiguration:
        """创建新的LLM配置"""
        db_obj = LLMConfiguration(
            id=str(uuid.uuid4()),
            name=config_in.name,
            role=config_in.role,
            provider=config_in.provider,
            model_name=config_in.model_name,
            prompt_template=config_in.prompt_template,
            additional_params=config_in.additional_params,
            api_key=config_in.api_key
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get(self, db: Session, config_id: str) -> Optional[LLMConfiguration]:
        """根据ID获取LLM配置"""
        return db.query(LLMConfiguration).filter(LLMConfiguration.id == config_id).first()

    def get_multi(
        self, db: Session, *, skip: int = 0, limit: int = 100
    ) -> List[LLMConfiguration]:
        """获取多个LLM配置"""
        return db.query(LLMConfiguration).offset(skip).limit(limit).all()

    def update(
        self, db: Session, *, db_obj: LLMConfiguration, obj_in: Dict[str, Any]
    ) -> LLMConfiguration:
        """更新LLM配置"""
        for field in obj_in:
            if field in obj_in and hasattr(db_obj, field):
                setattr(db_obj, field, obj_in[field])
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def delete(self, db: Session, *, config_id: str) -> LLMConfiguration:
        """删除LLM配置"""
        obj = db.query(LLMConfiguration).get(config_id)
        db.delete(obj)
        db.commit()
        return obj

# 创建一个实例供其他模块使用
crud_llm_config = CRUDLLMConfig()

def get_llm_config(db: Session, config_id: str) -> Optional[LLMConfiguration]:
    """获取单个LLM配置。
    
    Args:
        db: 数据库会话
        config_id: 配置ID
        
    Returns:
        Optional[LLMConfiguration]: 如找到，返回配置对象；否则返回None
    """
    return db.query(LLMConfiguration).filter(LLMConfiguration.id == config_id).first()

def get_llm_configs(db: Session, skip: int = 0, limit: int = 100) -> List[LLMConfiguration]:
    """获取LLM配置列表。
    
    Args:
        db: 数据库会话
        skip: 跳过的记录数
        limit: 返回的最大记录数
        
    Returns:
        List[LLMConfiguration]: 配置对象列表
    """
    return db.query(LLMConfiguration).offset(skip).limit(limit).all()

def create_llm_config(db: Session, config_in: common.LLMModelConfigInput) -> LLMConfiguration:
    """创建新的LLM配置。
    
    注意：API密钥不会被存储在数据库中。
    
    Args:
        db: 数据库会话
        config: LLM配置输入对象
        
    Returns:
        LLMConfiguration: 创建的配置对象
    """
    # API密钥处理：使用占位符，不存储实际值
    # 在实际调用时，客户端需要提供API密钥
    
    db_item = LLMConfiguration(
        id=str(uuid.uuid4()),
        role=config_in.role,
        name=config_in.name,
        provider=config_in.provider,
        model_name=config_in.model_name,
        prompt_template=config_in.prompt_template,
        additional_params=config_in.additional_params,
        api_key=config_in.api_key
    )
    db.add(db_item)
    db.commit()
    db.refresh(db_item)
    return db_item

def update_llm_config(
    db: Session, config_id: str, config_in: common.LLMModelConfigInput
) -> Optional[LLMConfiguration]:
    """更新LLM配置。
    
    Args:
        db: 数据库会话
        config_id: 配置ID
        config: 新的配置数据
        
    Returns:
        Optional[LLMConfiguration]: 更新后的配置对象，如果不存在返回None
    """
    db_item = get_llm_config(db, config_id)
    if not db_item:
        return None
    
    # 更新字段
    update_data = config_in.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_item, key, value)
    
    db.commit()
    db.refresh(db_item)
    return db_item

def delete_llm_config(db: Session, config_id: str) -> bool:
    """删除LLM配置。
    
    Args:
        db: 数据库会话
        config_id: 配置ID
        
    Returns:
        bool: 如成功删除返回True，否则返回False
    """
    db_item = get_llm_config(db, config_id)
    if not db_item:
        return False
    
    db.delete(db_item)
    db.commit()
    return True

def get_llm_config_by_name(db: Session, name: str) -> Optional[LLMConfiguration]:
    return db.query(LLMConfiguration).filter(LLMConfiguration.name == name).first() 