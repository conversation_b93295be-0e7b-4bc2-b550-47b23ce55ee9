from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
import uuid
from datetime import datetime

from app.models import common, db_models
from app.models.db_models import RetrievalAlgorithmConfiguration
from app.utils.db_utils import generate_uuid

class CRUDAlgorithmConfig:
    def create(self, db: Session, *, config_in: common.RetrievalAlgorithmConfigInput) -> RetrievalAlgorithmConfiguration:
        """创建新的检索算法配置"""
        db_obj = RetrievalAlgorithmConfiguration(
            id=str(uuid.uuid4()),
            name=config_in.name,
            corpus_extraction_endpoint=str(config_in.corpus_extraction_endpoint),
            corpus_extraction_method=config_in.corpus_extraction_method,
            corpus_extraction_params=config_in.corpus_extraction_params,
            corpus_extraction_headers=config_in.corpus_extraction_headers,
            similarity_search_endpoint=str(config_in.similarity_search_endpoint),
            similarity_search_method=config_in.similarity_search_method,
            similarity_search_input_template=config_in.similarity_search_input_template,
            similarity_search_headers=config_in.similarity_search_headers
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get(self, db: Session, config_id: str) -> Optional[RetrievalAlgorithmConfiguration]:
        """根据ID获取检索算法配置"""
        return db.query(RetrievalAlgorithmConfiguration).filter(RetrievalAlgorithmConfiguration.id == config_id).first()

    def get_multi(
        self, db: Session, *, skip: int = 0, limit: int = 100
    ) -> List[RetrievalAlgorithmConfiguration]:
        """获取多个检索算法配置"""
        return db.query(RetrievalAlgorithmConfiguration).offset(skip).limit(limit).all()

    def update(
        self, db: Session, *, db_obj: RetrievalAlgorithmConfiguration, obj_in: Dict[str, Any]
    ) -> RetrievalAlgorithmConfiguration:
        """更新检索算法配置"""
        for field in obj_in:
            if field in obj_in and hasattr(db_obj, field):
                setattr(db_obj, field, obj_in[field])
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def delete(self, db: Session, *, config_id: str) -> RetrievalAlgorithmConfiguration:
        """删除检索算法配置"""
        obj = db.query(RetrievalAlgorithmConfiguration).get(config_id)
        db.delete(obj)
        db.commit()
        return obj

# 创建一个实例供其他模块使用
crud_algorithm_config = CRUDAlgorithmConfig()

def get_algorithm_config(db: Session, config_id: str) -> Optional[RetrievalAlgorithmConfiguration]:
    """获取单个检索算法配置"""
    return db.query(RetrievalAlgorithmConfiguration).filter(RetrievalAlgorithmConfiguration.id == config_id).first()

def get_algorithm_config_by_name(db: Session, name: str) -> Optional[RetrievalAlgorithmConfiguration]:
    return db.query(RetrievalAlgorithmConfiguration).filter(RetrievalAlgorithmConfiguration.name == name).first()

def get_algorithm_configs(
    db: Session, skip: int = 0, limit: int = 100
) -> List[RetrievalAlgorithmConfiguration]:
    """获取检索算法配置列表"""
    return db.query(RetrievalAlgorithmConfiguration).offset(skip).limit(limit).all()

def create_algorithm_config(
    db: Session, config_in: common.RetrievalAlgorithmConfigInput
) -> db_models.RetrievalAlgorithmConfiguration:
    """创建新的检索算法配置"""
    db_obj = db_models.RetrievalAlgorithmConfiguration(
        id=generate_uuid(),
        name=config_in.name,
        corpus_extraction_api=config_in.corpus_extraction_api.model_dump(mode='json'),
        similarity_search_api=config_in.similarity_search_api.model_dump(mode='json'),
    )
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj

def update_algorithm_config(
    db: Session, config_id: str, config_update: common.RetrievalAlgorithmConfigInput
) -> Optional[db_models.RetrievalAlgorithmConfiguration]:
    """更新检索算法配置"""
    db_obj = get_algorithm_config(db, config_id)
    if db_obj:
        update_data = config_update.model_dump(exclude_unset=True)
        
        db_obj.name = update_data.get("name", db_obj.name)
        
        if "corpus_extraction_api" in update_data:
            # Ensure HttpUrl is serialized to string for JSON storage
            corpus_api_dict = config_update.corpus_extraction_api.model_dump(mode='json') 
            db_obj.corpus_extraction_api = corpus_api_dict
        if "similarity_search_api" in update_data:
            # Ensure HttpUrl is serialized to string for JSON storage
            similarity_api_dict = config_update.similarity_search_api.model_dump(mode='json')
            db_obj.similarity_search_api = similarity_api_dict
        
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
    return db_obj

def delete_algorithm_config(db: Session, config_id: str) -> bool:
    """删除检索算法配置"""
    db_item = get_algorithm_config(db, config_id)
    if not db_item:
        return False
    
    db.delete(db_item)
    db.commit()
    return True 