# FastAPI依赖注入项

# 示例：获取数据库会话的依赖项
# from app.db.session import SessionLocal # 假设你有一个db.session模块

# def get_db():
#     db = SessionLocal()
#     try:
#         yield db
#     finally:
#         db.close() 

from fastapi import Depends, HTTPException, Security, status
from sqlalchemy.orm import Session
from typing import Generator, Any

from app.core.database import get_db, SessionLocal
from app.core.security import get_api_key
from app.services.config_manager_service import ConfigManagerService
from app.services.api_caller_service import ApiCallerService, get_api_caller_service
from app.services.logger_service import LoggerService, get_logger_service
from app.services.result_storage_service import ResultStorageService, get_result_storage_service
from app.services.experiment_orchestrator_service import ExperimentOrchestratorService

# 数据库依赖，这将在app/core/database.py中定义
# def get_db():
#     pass

# Define get_db_session, which is used by get_config_manager_service
def get_db_session() -> Generator[Session, Any, None]:
    db = SessionLocal() # SessionLocal is imported from app.core.database
    try:
        yield db
    finally:
        db.close()

# Factory function for ConfigManagerService, moved here to break circular import
def get_config_manager_service(db: Session = Depends(get_db_session)) -> ConfigManagerService:
    """获取配置管理服务实例。
    
    Args:
        db: 数据库会话 (通过 get_db_session 注入)
        
    Returns:
        ConfigManagerService: 服务实例
    """
    return ConfigManagerService(db=db)

# 安全依赖项
def get_authenticated_db(
    api_key: str = Security(get_api_key),
    db: Session = Depends(get_db)
):
    """结合API密钥验证和数据库会话的依赖。
    
    这个依赖可用于需要认证的API端点，它会：
    1. 验证API密钥
    2. 提供数据库会话
    
    Args:
        api_key: 通过Security依赖验证的API密钥
        db: 数据库会话
    
    Returns:
        Session: 数据库会话，仅当API密钥验证通过
    """
    return db

# 配置管理服务依赖
def get_config_manager(db: Session = Depends(get_authenticated_db)) -> ConfigManagerService:
    """提供配置管理服务实例。
    
    带认证的配置管理服务依赖，用于需要API密钥保护的端点。
    
    Args:
        db: 已通过认证的数据库会话
    
    Returns:
        ConfigManagerService: 配置管理服务实例
    """
    return get_config_manager_service(db)

# API调用服务依赖
def get_api_caller(config_manager: ConfigManagerService = Depends(get_config_manager)) -> ApiCallerService:
    """提供API调用服务实例。
    
    Args:
        config_manager: 通过依赖注入获取的 ConfigManagerService 实例。

    Returns:
        ApiCallerService: API调用服务实例
    """
    return get_api_caller_service(config_manager=config_manager)

# 日志服务依赖
def get_logger() -> LoggerService:
    """提供日志服务实例。
    
    Returns:
        LoggerService: 日志服务实例
    """
    return get_logger_service()

# 结果存储服务依赖
def get_result_storage() -> ResultStorageService:
    """提供结果存储服务实例。
    
    Returns:
        ResultStorageService: 结果存储服务实例
    """
    return get_result_storage_service()

# 实验编排器服务依赖
def get_experiment_orchestrator(
    db: Session = Depends(get_authenticated_db),
    config_service: ConfigManagerService = Depends(get_config_manager),
    api_caller: ApiCallerService = Depends(get_api_caller),
    logger_service: LoggerService = Depends(get_logger),
    result_storage: ResultStorageService = Depends(get_result_storage)
) -> ExperimentOrchestratorService:
    """提供实验编排器服务实例。
    
    Args:
        db: 已通过认证的数据库会话
        config_service: 配置管理服务实例
        api_caller: API调用服务实例
        logger_service: 日志服务实例
        result_storage: 结果存储服务实例
    
    Returns:
        ExperimentOrchestratorService: 实验编排器服务实例
    """
    return ExperimentOrchestratorService(
        config_service=config_service,
        api_caller=api_caller,
        logger_service=logger_service,
        result_storage=result_storage
    ) 