from fastapi import APIRouter, HTTPException, Depends, status, Body
from fastapi.responses import FileResponse
from typing import List, Optional, Any
import os
import asyncio
import uuid
import logging
from pydantic import BaseModel, Field
from app.models import common
from app.services.experiment_orchestrator_service import ExperimentOrchestratorService
from app.apis.deps import get_result_storage, get_logger, get_experiment_orchestrator
from app.services.result_storage_service import ResultStorageService
from app.services.logger_service import LoggerService
from app.services.config_manager_service import ConfigManagerService
from app.apis.deps import get_config_manager_service
from app.tasks import run_experiment_task # 导入Celery任务
from app.core.celery_worker import celery_app
from app.core.database import get_db # 导入 get_db
from app.crud import crud_experiment_run # 导入 crud_experiment_run
from sqlalchemy.orm import Session # 导入 Session

logger = logging.getLogger(__name__)

router = APIRouter()

# 定义 ExperimentRunRequest Pydantic 模型 (根据设计文档 任务 4.2.8)
class ExperimentRunRequest(BaseModel):
    """实验运行请求体定义。"""
    algorithm_config_id: str
    qa_generator_model_config_id: str
    answer_predictor_model_config_id: str
    evaluator_model_config_id: str
    # API密钥字段已被移除，将通过config_id在后端内部获取
    # qa_generator_api_key: str
    # answer_predictor_api_key: str
    # evaluator_api_key: str
    max_corpus_items: Optional[int] = Field(default=10, gt=0)
    custom_run_id: Optional[str] = None

class ExperimentTaskSubmissionResponse(BaseModel):
    """实验任务提交后的响应体"""
    message: str
    run_id: str
    task_id: str
    status_url: str

@router.post(
    "/run", 
    response_model=common.BatchExperimentRunResponse, # 响应模型改为批量响应
    status_code=status.HTTP_202_ACCEPTED, 
    summary="异步运行一个或多个实验轮次", 
    description="根据指定的实验任务配置，提交一个或多个实验到后台执行。"
)
async def run_multiple_experiments_async(
    request_data: common.ExperimentRunRequest = Body(...), # 使用 common 中的请求模型
    config_service: ConfigManagerService = Depends(get_config_manager_service), # Correctly depends on the moved function
    db: Session = Depends(get_db) # 注入数据库会话
):
    """
    提交一个新的实验运行任务（或一批任务）。
    此端点会将实验请求加入到后台任务队列中进行处理。
    """
    
    submissions: List[common.SingleExperimentTaskSubmissionResponse] = []
    
    # 1. 获取实验任务配置以验证并获取默认参数
    task_config = await config_service.get_full_experiment_task_config(request_data.experiment_task_config_id)
    if not task_config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"ID为 {request_data.experiment_task_config_id} 的实验任务配置未找到。"
        )

    actual_max_corpus_items = request_data.max_corpus_items_override if request_data.max_corpus_items_override is not None else task_config.default_max_corpus_items

    base_run_id = request_data.base_custom_run_id
    
    for i in range(request_data.num_repetitions):
        current_run_id_candidate: str
        
        if base_run_id:
            # 如果提供了基础ID，尝试 base_id_1, base_id_2 ...
            current_suffix = i + 1
            while True:
                current_run_id_candidate = f"{base_run_id}_{current_suffix}"
                if not crud_experiment_run.is_run_id_exists(db, current_run_id_candidate):
                    break
                current_suffix += 1 # 如果已存在，尝试下一个后缀
        else:
            # 如果没有提供基础ID，使用UUID，并理论上检查唯一性（尽管碰撞概率极低）
            while True:
                current_run_id_candidate = str(uuid.uuid4())
                if not crud_experiment_run.is_run_id_exists(db, current_run_id_candidate):
                    break
        
        # 最终确定的唯一 run_id
        final_run_id = current_run_id_candidate

        try:
            celery_task_instance = run_experiment_task.delay(
                run_id=final_run_id, # 使用最终确定的唯一 run_id
                experiment_task_config_id=request_data.experiment_task_config_id,
                max_corpus_items_override=actual_max_corpus_items
            )
            
            api_base_path = "/api/v1/experiments" 
            submission_response = common.SingleExperimentTaskSubmissionResponse(
                celery_task_id=celery_task_instance.id,
                run_id=final_run_id, # 使用最终确定的唯一 run_id
                status_url=f"{api_base_path}/tasks/{celery_task_instance.id}/status",
                report_url=f"{api_base_path}/reports/{final_run_id}" # 使用最终确定的唯一 run_id
            )
            submissions.append(submission_response)
            
        except Exception as e:
            logger.error(f"提交实验任务 (run_id: {final_run_id}) 失败: {str(e)}", exc_info=True)
            # 如果单个任务提交失败，可以选择是否继续提交其他任务。
            # 当前逻辑是记录错误并继续（pass），如果所有都失败，则在末尾抛出异常。
            # 如果需要，可以在这里添加更具体的错误处理或提前终止。
            pass 

    if not submissions and request_data.num_repetitions > 0:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="所有实验任务提交均失败，请检查Celery Broker连接或服务器日志。"
        )

    return common.BatchExperimentRunResponse(
        requested_repetitions=request_data.num_repetitions,
        base_custom_run_id=request_data.base_custom_run_id,
        submissions=submissions
        )

# --- Phase 6: 结果与日志API ---

@router.get("/reports/{run_id}", response_model=common.ExperimentRunReport, summary="获取单个实验报告")
async def get_experiment_report(
    run_id: str,
    result_storage_service: ResultStorageService = Depends(get_result_storage)
):
    """根据实验运行ID获取详细的实验报告。"""
    report = await result_storage_service.get_experiment_run_report(run_id)
    if not report:
        raise HTTPException(status_code=404, detail=f"实验报告 {run_id} 未找到")
    return report

@router.get(
    "/reports",
    response_model=List[common.ExperimentRunReportSummary],
    summary="获取所有实验运行摘要列表"
)
async def list_experiment_reports_endpoint(
    result_storage_service: ResultStorageService = Depends(get_result_storage)
):
    """
    获取所有已完成实验运行的摘要信息列表。
    """
    try:
        summaries = await result_storage_service.get_all_run_summaries()
        if summaries is None: 
            return []
        return summaries
    except Exception as e:
        logger.error(f"获取实验报告摘要列表时出错: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取实验报告摘要列表时出错: {str(e)}")

@router.get("/logs/{run_id}", response_class=FileResponse, summary="下载指定实验的日志文件")
async def download_experiment_log(
    run_id: str,
    logger_service: LoggerService = Depends(get_logger)
):
    """
    根据实验运行ID下载对应的JSONL格式日志文件。
    如果日志文件不存在，将返回404错误。
    """
    log_file_path = logger_service.get_log_file_path_for_run(run_id)

    if not log_file_path: 
        raise HTTPException(status_code=404, detail=f"实验 {run_id} 的日志文件未找到或无权访问")
    
    if not await asyncio.to_thread(os.path.exists, log_file_path):
        raise HTTPException(status_code=404, detail=f"实验 {run_id} 的日志文件在路径 {log_file_path} 未找到")

    return FileResponse(path=log_file_path, media_type='application/x-jsonlines', filename=f"run_{run_id}_log.jsonl")

# 新增API端点用于任务管理
class CeleryTaskStatusResponse(BaseModel): # Renamed from TaskStatusResponse to avoid conflict if any
    task_id: str
    status: str
    result_run_id: Optional[str] = None # If successful, Celery task returns run_id
    error_message: Optional[str] = None
    traceback_info: Optional[str] = None # For more detailed error info from Celery

@router.get("/tasks/{task_id}/status", response_model=CeleryTaskStatusResponse, name="get_experiment_task_status", summary="获取实验任务状态")
async def get_task_status_endpoint(task_id: str):
    """
    查询特定实验任务的当前状态和结果（如果已完成）。
    """
    task_result = celery_app.AsyncResult(task_id)
    
    response_data = {
        "task_id": task_id,
        "status": task_result.status,
    }

    if task_result.successful():
        # Celery task `run_experiment_task` returns `run_id` on success
        response_data["result_run_id"] = task_result.result 
    elif task_result.failed():
        error_info = task_result.result # Exception instance
        response_data["error_message"] = f"任务失败: {type(error_info).__name__} - {str(error_info)}"
        response_data["traceback_info"] = task_result.traceback
    elif task_result.status == 'PENDING':
        response_data["error_message"] = "任务正在等待执行或不存在。"
    elif task_result.status == 'STARTED' or task_result.status == 'RETRY':
        response_data["error_message"] = f"任务当前状态: {task_result.status}"
    else: # UNKNOWN or other states
        response_data["error_message"] = f"任务状态未知: {task_result.status}. Celery可能无法追踪此任务，或者它已过期。"
        # For unknown tasks, Celery often returns a PENDING state as well, 
        # or state is UNKNOWN if backend doesn't have it.
        # Consider if CELERY_TASK_UNKNOWN from common.ExperimentRunStatus should be used in ExperimentRun record
        # if we can detect this scenario more reliably when creating/updating ExperimentRun DB entry.
    
    return CeleryTaskStatusResponse(**response_data)

class TaskRevokeResponse(BaseModel):
    task_id: str
    status_message: str

@router.post("/tasks/{task_id}/stop", response_model=TaskRevokeResponse, summary="停止实验任务")
async def stop_task_endpoint(task_id: str):
    """
    尝试撤销（停止）一个正在进行或待处理的实验任务。
    注意: 撤销操作的成功取决于任务的当前状态和Celery worker的处理。
    """
    try:
        # Check current state first, revoke might not be effective for already completed/failed tasks
        task_result = celery_app.AsyncResult(task_id)
        if task_result.ready(): # True if task has executed (SUCCESS, FAILURE, REVOKED)
             return TaskRevokeResponse(
                task_id=task_id,
                status_message=f"任务已完成 (状态: {task_result.status})，无法停止。"
            )

        celery_app.control.revoke(task_id, terminate=True, signal='SIGTERM')
        return TaskRevokeResponse(
            task_id=task_id,
            status_message="已发送任务停止请求。请检查任务状态以确认。"
        )
    except Exception as e: 
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"无法发送停止任务请求: {str(e)}"
        )

@router.get("/tasks/results/{identifier}", 
            response_model=common.ExperimentRunReport, 
            summary="获取实验任务结果 (通过Celery任务ID或直接用RunID)", 
            description="根据Celery任务ID或运行ID获取已完成实验的报告。如果提供任务ID且成功，则使用其返回的run_id查找报告。否则，直接将identifier作为run_id查找。"
)
async def get_task_result_endpoint(
    identifier: str,
    result_storage_service: ResultStorageService = Depends(get_result_storage)
    # No db session needed here directly, as it only interacts with result_storage and task status endpoint
):
    potential_run_id = identifier 

    # Heuristically check if identifier might be a Celery task ID
    # A more robust check might involve trying to parse it as a UUID if Celery task IDs are UUIDs
    is_likely_task_id = len(identifier) > 10 and ("-" in identifier or identifier.isalnum()) 
    # This is a very loose check, Celery task IDs can be non-UUID. 
    # Relying on AsyncResult not erroring for a run_id is also part of the logic flow.

    if is_likely_task_id:
        try:
            task_status = await get_task_status_endpoint(identifier) 
            if task_status.status == "SUCCESS" and task_status.result_run_id:
                potential_run_id = task_status.result_run_id
            elif task_status.status == "FAILURE":
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Celery任务 {identifier} 执行失败: {task_status.error_message}")
            # If task is PENDING, STARTED, RETRY, or UNKNOWN, we will proceed to try identifier as run_id
        except HTTPException as http_exc: # If get_task_status_endpoint itself raises 404 for task_id not found
            if http_exc.status_code == 404:
                 pass # Task ID not found in Celery, proceed to treat identifier as run_id
        except Exception: # Catch any other error from AsyncResult or status logic, treat identifier as run_id
            pass 
    report = await result_storage_service.get_experiment_run_report(potential_run_id)
    if report:
        return report
        
    # If report is not found using potential_run_id (either original identifier or one from Celery task)
    raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"无法找到与标识符 '{identifier}' 对应的实验报告。")

# 清理掉之前用于直接运行实验的端点 (如果还存在且名为 run_experiment_endpoint)
# router.routes = [route for route in router.routes if getattr(route, "name", "") != "run_experiment_endpoint"]

# 注意: 还需要在 app/apis/v1/__init__.py 和 app/main.py 中注册这个router (已完成) 