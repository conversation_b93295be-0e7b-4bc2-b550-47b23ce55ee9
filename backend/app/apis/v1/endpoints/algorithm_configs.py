from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.models import common
from app.services.config_manager_service import ConfigManagerService
from app.apis.deps import get_config_manager_service, get_db_session
from app.core.security import get_api_key
from app.core.logger import logger

router = APIRouter()

# 检索算法配置 CRUD 操作
@router.post(
    "/", 
    response_model=common.RetrievalAlgorithmConfig, 
    status_code=status.HTTP_201_CREATED,
    dependencies=[Depends(get_api_key)],
    summary="创建新的检索算法配置",
    description="创建一个新的检索算法配置，包括其与知识库API的交互方式和要使用的相似度算法名称。"
)
async def create_algorithm_config_endpoint(
    config_in: common.RetrievalAlgorithmConfigInput,
    service: ConfigManagerService = Depends(get_config_manager_service)
):
    logger.info(f"API: Attempting to create retrieval algorithm config: {config_in.name}")
    try:
        # Name uniqueness check is now handled in the service layer
        created_config = await service.create_retrieval_algorithm_config(config_in)
        logger.info(f"API: Successfully created retrieval algorithm config '{created_config.name}' with id {created_config.id}")
        return created_config
    except HTTPException as http_exc: # Catch service-level HTTPExceptions (like 409 Conflict)
        logger.warning(f"API: HTTPException during creation of algorithm config '{config_in.name}': {http_exc.detail}")
        raise http_exc
    except Exception as e:
        logger.error(f"API: Unexpected error creating algorithm config '{config_in.name}': {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建检索算法配置 '{config_in.name}' 时发生内部服务器错误。"
        )

@router.get(
    "/", 
    response_model=List[common.RetrievalAlgorithmConfig],
    dependencies=[Depends(get_api_key)],
    summary="获取所有检索算法配置",
    description="获取系统中所有已保存的检索算法配置列表。"
)
async def get_all_algorithm_configs_endpoint(
    service: ConfigManagerService = Depends(get_config_manager_service)
):
    logger.debug("API: Fetching all retrieval algorithm configs")
    return await service.get_all_retrieval_algorithm_configs()

@router.get(
    "/{config_id}", 
    response_model=common.RetrievalAlgorithmConfig,
    dependencies=[Depends(get_api_key)],
    summary="根据ID获取单个检索算法配置",
    description="通过其唯一ID获取指定的检索算法配置详情。"
)
async def get_algorithm_config_by_id_endpoint(
    config_id: str,
    service: ConfigManagerService = Depends(get_config_manager_service)
):
    logger.debug(f"API: Fetching retrieval algorithm config with id: {config_id}")
    config = await service.get_retrieval_algorithm_config(config_id)
    if not config:
        logger.warning(f"API: Retrieval algorithm config with id '{config_id}' not found.")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="检索算法配置未找到")
    return config

@router.put(
    "/{config_id}", 
    response_model=common.RetrievalAlgorithmConfig,
    dependencies=[Depends(get_api_key)],
    summary="更新检索算法配置",
    description="通过其ID更新现有的检索算法配置。所有字段都会被替换为提供的值。"
)
async def update_algorithm_config_endpoint(
    config_id: str,
    config_update: common.RetrievalAlgorithmConfigInput,
    service: ConfigManagerService = Depends(get_config_manager_service)
):
    logger.info(f"API: Attempting to update retrieval algorithm config with id: {config_id}")
    try:
        updated_config = await service.update_retrieval_algorithm_config(config_id, config_update)
        if not updated_config:
            logger.warning(f"API: Update failed. Retrieval algorithm config with id '{config_id}' not found.")
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="检索算法配置未找到，无法更新")
        logger.info(f"API: Successfully updated retrieval algorithm config '{updated_config.name}' (id: {config_id})")
        return updated_config
    except HTTPException as http_exc: # Catch service-level HTTPExceptions (like 409 Conflict for name)
        logger.warning(f"API: HTTPException during update of algorithm config '{config_id}': {http_exc.detail}")
        raise http_exc
    except Exception as e:
        logger.error(f"API: Unexpected error updating algorithm config '{config_id}': {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新检索算法配置 '{config_id}' 时发生内部服务器错误。"
        )

@router.delete(
    "/{config_id}",
    response_model=common.MessageResponse, 
    dependencies=[Depends(get_api_key)],
    summary="删除检索算法配置",
    description="通过其ID删除指定的检索算法配置。如果配置被其他实验任务使用，可能无法删除。"
)
async def delete_algorithm_config_endpoint(
    config_id: str,
    service: ConfigManagerService = Depends(get_config_manager_service)
):
    logger.info(f"API: Attempting to delete retrieval algorithm config with id: {config_id}")
    try:
        deleted = await service.delete_retrieval_algorithm_config(config_id)
        if not deleted:
            logger.warning(f"API: Deletion failed. Retrieval algorithm config with id '{config_id}' not found or not deleted.")
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="检索算法配置未找到或删除失败")
        message = f"检索算法配置 '{config_id}' 已成功删除。"
        logger.info(f"API: {message}")
        return common.MessageResponse(message=message)
    except HTTPException as http_exc: # Catch service-level HTTPExceptions (like 409 Conflict if in use)
        logger.warning(f"API: HTTPException during deletion of algorithm config '{config_id}': {http_exc.detail}")
        raise http_exc
    except Exception as e:
        logger.error(f"API: Unexpected error deleting algorithm config '{config_id}': {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除检索算法配置 '{config_id}' 时发生内部服务器错误。"
        )

@router.post(
    "/{config_id}/test", 
    response_model=Dict[str, Any], # Or a more specific Pydantic model for test results
    dependencies=[Depends(get_api_key)],
    summary="测试检索算法配置连接性",
    description="测试指定检索算法配置中的知识库API端点是否可访问和响应。"
)
async def test_algorithm_config_connection_endpoint(
    config_id: str,
    service: ConfigManagerService = Depends(get_config_manager_service)
):
    logger.info(f"API: Testing connection for retrieval algorithm config id: {config_id}")
    try:
        test_result = await service.test_retrieval_algorithm_config(config_id)
        # The service method now raises HTTPException for 404, so no need to check for None here
        logger.info(f"API: Connection test for algorithm config '{config_id}' completed. Overall status: {test_result.get('overall_status')}")
        return test_result
    except HTTPException as http_exc:
        logger.warning(f"API: HTTPException during connection test for algorithm config '{config_id}': {http_exc.detail}")
        raise http_exc # Re-raise service-level exceptions (e.g., 404)
    except Exception as e:
        logger.error(f"API: Unexpected error testing algorithm config '{config_id}': {e}", exc_info=True)
        # Return a 500 error with a generic message for unexpected errors
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"测试算法配置 '{config_id}' 连接时发生意外错误: {str(e)}"
        ) 