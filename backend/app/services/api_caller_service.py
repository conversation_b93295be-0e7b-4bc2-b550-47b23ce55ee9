from __future__ import annotations
from typing import Dict, Any, Optional, TYPE_CHECKING
import httpx
from pydantic import HttpUrl
import json
import logging

# 从 llm_api_integrator 导入所需组件
from llm_api_integrator import LLMFactory, LLMInterface
from llm_api_integrator.exceptions import LLMAPIError, AuthenticationError, RateLimitError, ModelNotFoundError, InvalidRequestError, APITimeoutError

# 从 app.models import common
from app.models import common

# 移除直接导入
# from app.services.config_manager_service import ConfigManagerService

logger = logging.getLogger(__name__)

if TYPE_CHECKING: # 添加条件导入块
    from app.services.config_manager_service import ConfigManagerService

class ApiCallerService:
    """服务于调用外部API，包括LLM和通用HTTP接口。"""
    def __init__(self, 
                 config_manager: "ConfigManagerService", # 使用引号进行前向引用
                 http_client: Optional[httpx.AsyncClient] = None):
        """初始化ApiCallerService。

        Args:
            config_manager: ConfigManagerService实例。
            http_client: 可选的httpx.AsyncClient实例。如果未提供，则会创建一个新的实例。
        """
        self.http_client = http_client or httpx.AsyncClient(timeout=60.0) # 超时时间修改为60秒
        self.config_manager = config_manager # 在实例化时注入

    async def call_llm_api(
        self,
        llm_config_id: str, # 改为接收配置ID
        prompt: str,
        # 如果 ConfigManagerService 不是在 __init__ 中注入，则需要在这里传入 db Session
        # db_session_for_config: Session 
    ) -> str:
        #  移除对 self.config_manager 的检查，因为它现在是必需的
        # if not self.config_manager:
        #     # 这个检查是为了防止 config_manager 未被正确注入
        #     # 在实际应用中，FastAPI 的依赖注入系统会处理这个问题
        #     # 如果是 Celery 任务，则需要确保 Celery 任务能获取到 ConfigManagerService 实例
        #     raise ValueError("ConfigManagerService not initialized in ApiCallerService")

        # 从 ConfigManagerService 获取配置，包括密钥
        # 注意：get_llm_model_config_with_key 返回的是 common.LLMModelConfig，包含 api_key
        config_with_key = await self.config_manager.get_llm_model_config_with_key(llm_config_id)
        
        if not config_with_key:
            raise ValueError(f"LLM Configuration with ID '{llm_config_id}' not found.")
        if not config_with_key.api_key:
            raise ValueError(f"API key not set for LLM Configuration ID '{llm_config_id}'.")

        try:
            llm_client: LLMInterface = LLMFactory.get_llm_client(
                provider_name=config_with_key.provider,
                model_name=config_with_key.model_name,
                api_key=config_with_key.api_key, # 使用从数据库获取的密钥
                # base_url= (根据需要从配置或环境变量获取)
            )
            
            response = await llm_client.async_generate_response(
                prompt=prompt,
                stream=False, # 假设非流式
                **(config_with_key.additional_params or {})
            )
            if isinstance(response, str): # 对于非流式调用
                return response
            else: # 处理可能的异步生成器或其他复杂返回类型（如果适用）
                # 此处简化为仅返回字符串，实际可能需要更复杂的处理
                logger.error(f"Unexpected response type from LLM for config {llm_config_id}: {type(response)}")
                raise ValueError("Unexpected response type from LLM.")

        except AuthenticationError as e:
            logger.error(f"LLM Authentication Error for config {llm_config_id}: {e}")
            raise ValueError(f"LLM Authentication failed: {e}")
        except RateLimitError as e:
            logger.error(f"LLM Rate Limit Error for config {llm_config_id}: {e}")
            raise ValueError(f"LLM rate limit exceeded: {e}")
        except Exception as e:
            logger.error(f"Error calling LLM API for config {llm_config_id}: {e}")
            raise ValueError(f"Failed to call LLM API: {e}")

    async def call_external_http_api(
        self,
        url: HttpUrl,
        method: str = "GET",
        params: Optional[Dict[str, Any]] = None, # 这个 params 用于显式传入的查询参数
        json_data: Optional[Dict[str, Any]] = None, # 这个 json_data 代表配置中的 body
        headers: Optional[Dict[str, str]] = None
    ) -> httpx.Response:
        """通用方法，用于请求外部HTTP服务。"""
        
        effective_method = method.upper()
        request_url = str(url)
        query_params_to_send = params.copy() if params else {}
        actual_json_payload = json_data

        if effective_method == "GET" and json_data:
            # 如果是GET请求且json_data (来自配置中的body) 存在，则将其内容作为查询参数
            logger.debug(f"GET请求检测到body内容 (json_data)，将转换为查询参数。原始URL: {request_url}")
            for key, value in json_data.items():
                if value is not None: # 过滤掉值为None的参数
                    query_params_to_send[key] = value
            actual_json_payload = None # GET请求不应有json请求体
            logger.debug(f"转换后的查询参数: {query_params_to_send}")

        logger.debug(f"调用外部HTTP API: Method={effective_method}, URL={request_url}, QueryParams={query_params_to_send}, JSON Payload Present={actual_json_payload is not None}")
        
        try:
            response = await self.http_client.request(
                method=effective_method,
                url=request_url, # URL本身不在这里修改以附加查询参数
                params=query_params_to_send, # httpx 会处理将此字典附加到URL
                json=actual_json_payload, # 对于GET，这里应该是None
                headers=headers
            )
            response.raise_for_status() # 如果是4xx或5xx响应，则引发HTTPStatusError
            logger.debug(f"外部HTTP API调用成功: URL={response.url}, Status={response.status_code}") # 使用 response.url 获取实际请求的URL
            return response
        except httpx.TimeoutException as e:
            logger.error(f"外部API调用超时: URL={url}. Error: {e}")
            raise APITimeoutError(f"请求外部API {url} 超时") # 可以复用或定义新的超时异常
        except httpx.HTTPStatusError as e:
            logger.error(f"外部API调用HTTP错误: URL={url}, Status={e.response.status_code}. Response: {e.response.text[:200]}")
            # 可以根据 e.response.status_code 转换为更具体的错误
            raise InvalidRequestError(f"外部API {url} 返回错误状态 {e.response.status_code}: {e.response.text[:200]}")
        except httpx.RequestError as e:
            # 其他请求相关错误 (如连接错误)
            logger.error(f"外部API请求错误: URL={url}. Error: {e}")
            raise LLMAPIError(f"请求外部API {url} 失败: {e}") # 使用一个通用错误类型或定义新的
        except Exception as e:
            logger.error(f"调用外部HTTP API时发生意外错误: URL={url}. Error: {e}")
            raise RuntimeError(f"调用外部HTTP API {url} 时发生意外错误: {e}")

    async def close(self):
        """关闭内部的httpx.AsyncClient会话。"""
        await self.http_client.aclose()

# 工厂函数，用于依赖注入
def get_api_caller_service(config_manager: "ConfigManagerService") -> ApiCallerService:
    """获取API调用服务实例。
    
    Args:
        config_manager: ConfigManagerService 实例。

    Returns:
        ApiCallerService: 服务实例
    """
    return ApiCallerService(config_manager=config_manager) 