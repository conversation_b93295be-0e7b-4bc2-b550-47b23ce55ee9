from __future__ import annotations
from typing import List, Optional, Dict, Any, TYPE_CHECKING
from sqlalchemy.orm import Session
import httpx
from fastapi import HTTPException, status
import json

from app.models import common
from app.models.db_models import LLMConfiguration, RetrievalAlgorithmConfiguration, ExperimentTaskConfiguration, ExperimentRun
from app.crud import crud_llm_config, crud_algorithm_config, crud_experiment_task_config, crud_experiment_run
from llm_api_integrator import LLMFactory
from llm_api_integrator.exceptions import LLMAPIError, AuthenticationError, RateLimitError, ModelNotFoundError, InvalidRequestError, APITimeoutError
# from app.services.api_caller_service import ApiCallerService

import logging
logger = logging.getLogger(__name__)

if TYPE_CHECKING:
    from app.services.api_caller_service import ApiCallerService

# from fastapi import Depends
# from app.apis.deps import get_db_session

class ConfigManagerService:
    """配置管理服务。
    
    封装LLM配置和检索算法配置的管理操作。
    """
    
    def __init__(self, db: Session):
        """初始化服务。
        
        Args:
            db: 数据库会话
        """
        self.db = db
        self.http_client = httpx.AsyncClient(timeout=10.0)  # 用于连接测试
    
    # LLM配置管理
    async def create_llm_model_config(self, config_in: common.LLMModelConfigInput) -> common.LLMModelConfig:
        """创建LLM模型配置。
        
        Args:
            config_in: 配置输入对象
            
        Returns:
            common.LLMModelConfig: 创建的配置对象
        """
        if crud_llm_config.get_llm_config_by_name(self.db, name=config_in.name):
            raise ValueError(f"LLM配置名称 '{config_in.name}' 已存在.")
        
        db_item = crud_llm_config.create_llm_config(self.db, config_in=config_in)
        return common.LLMModelConfig.model_validate(db_item)
    
    async def get_llm_model_config(self, config_id: str) -> Optional[common.LLMModelConfig]:
        """获取LLM模型配置。
        
        Args:
            config_id: 配置ID
            
        Returns:
            Optional[common.LLMModelConfig]: 配置对象，如不存在返回None
        """
        db_item = crud_llm_config.get_llm_config(self.db, config_id=config_id)
        if db_item:
            return common.LLMModelConfig.model_validate(db_item)
        return None
    
    async def get_llm_model_config_with_key(self, config_id: str) -> Optional[common.LLMModelConfig]:
        """此方法内部使用，可以返回带key的配置"""
        db_item = crud_llm_config.get_llm_config(self.db, config_id=config_id)
        if db_item:
            return common.LLMModelConfig.model_validate(db_item)
        return None
    
    async def get_all_llm_model_configs(self, skip: int = 0, limit: int = 100) -> List[common.LLMModelConfig]:
        """获取所有LLM模型配置。
        
        Args:
            skip: 跳过的记录数
            limit: 返回的最大记录数
            
        Returns:
            List[common.LLMModelConfig]: 配置对象列表
        """
        db_items = crud_llm_config.get_llm_configs(self.db, skip, limit)
        return [common.LLMModelConfig.model_validate(item) for item in db_items]
    
    async def update_llm_model_config(
        self, config_id: str, config_in: common.LLMModelConfigInput
    ) -> Optional[common.LLMModelConfig]:
        """更新LLM模型配置。
        
        Args:
            config_id: 配置ID
            config_in: 新的配置数据
            
        Returns:
            Optional[common.LLMModelConfig]: 更新后的配置对象，如不存在返回None
        """
        existing_by_name = crud_llm_config.get_llm_config_by_name(self.db, name=config_in.name)
        if existing_by_name and existing_by_name.id != config_id:
            raise ValueError(f"另一个LLM配置名称 '{config_in.name}' 已存在.")

        db_item = crud_llm_config.update_llm_config(self.db, config_id=config_id, config_in=config_in)
        if db_item:
            return common.LLMModelConfig.model_validate(db_item)
        return None
    
    async def delete_llm_model_config(self, config_id: str) -> bool:
        """删除LLM模型配置。
        
        Args:
            config_id: 配置ID
            
        Returns:
            bool: 删除成功（找到并删除）返回True，否则返回False
        """
        # crud_llm_config.delete_llm_config 本身就返回 bool
        was_deleted = crud_llm_config.delete_llm_config(self.db, config_id=config_id)
        return was_deleted
    
    async def test_llm_model_config(self, config_id: str) -> Dict[str, Any]:
        """测试LLM模型配置的连通性和有效性。

        Args:
            config_id: 要测试的LLM模型配置ID。

        Returns:
            Dict[str, Any]: 包含测试结果的字典。
        """
        logger.info(f"开始测试LLM模型配置: {config_id}")
        llm_config = await self.get_llm_model_config_with_key(config_id) # 使用 with_key 获取 api_key

        if not llm_config:
            logger.warning(f"LLM配置测试失败：未找到ID为 '{config_id}' 的配置。")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"未找到ID为 '{config_id}' 的LLM配置。"
            )

        test_result = {
            "config_id": config_id,
            "name": llm_config.name,
            "provider": llm_config.provider,
            "model_name": llm_config.model_name,
            "status": "pending",
            "details": "尚未开始测试",
            "error_type": None,
            "error_message": None
        }

        try:
            logger.debug(f"使用 LLMFactory 获取 '{llm_config.provider}' 的 '{llm_config.model_name}' 模型实例进行测试。")
            # 确保 api_key 传递给 LLMFactory
            # LLMFactory.get_llm 需要一个参数字典，其中可以包含 api_key
            factory_params = {
                # "model_name": llm_config.model_name, # 将作为 get_llm_client 的顶级参数
                # "api_key": llm_config.api_key, # 将作为 get_llm_client 的顶级参数
            }
            if llm_config.additional_params:
                factory_params.update(llm_config.additional_params)
            
            # 从 additional_params 中移除 prompt_template，因为它不适用于 factory 初始化
            # 也移除 model_name 和 api_key 如果它们可能存在于 additional_params 中，因为它们是顶级参数
            factory_params.pop("prompt_template", None)
            factory_params.pop("model_name", None) # 确保不重复传递
            factory_params.pop("api_key", None)    # 确保不重复传递

            llm_instance = LLMFactory.get_llm_client(
                provider_name=llm_config.provider,
                api_key=llm_config.api_key, 
                model_name=llm_config.model_name,
                **factory_params
            )
            
            # 执行一个简单的、低成本的API调用来测试连接和认证
            # 例如，发送一个非常短的提示
            logger.debug(f"向模型 '{llm_config.model_name}' 发送测试提示。")
            await llm_instance.async_generate_response("Test prompt", stream=False) # 或者其他适合的测试调用, 确保非流式
            
            test_result["status"] = "success"
            test_result["details"] = "成功连接到LLM服务并通过基本API调用验证。"
            logger.info(f"LLM配置 '{config_id}' 测试成功。")

        except AuthenticationError as e:
            test_result["status"] = "failed"
            test_result["error_type"] = "AuthenticationError"
            test_result["error_message"] = str(e)
            test_result["details"] = f"API认证失败: {str(e)}"
            logger.warning(f"LLM配置 '{config_id}' 测试失败 (认证错误): {e}")
        except RateLimitError as e:
            test_result["status"] = "failed"
            test_result["error_type"] = "RateLimitError"
            test_result["error_message"] = str(e)
            test_result["details"] = f"达到API速率限制: {str(e)}"
            logger.warning(f"LLM配置 '{config_id}' 测试失败 (速率限制): {e}")
        except ModelNotFoundError as e:
            test_result["status"] = "failed"
            test_result["error_type"] = "ModelNotFoundError"
            test_result["error_message"] = str(e)
            test_result["details"] = f"模型未找到: {str(e)}"
            logger.warning(f"LLM配置 '{config_id}' 测试失败 (模型未找到): {e}")
        except InvalidRequestError as e:
            test_result["status"] = "failed"
            test_result["error_type"] = "InvalidRequestError"
            test_result["error_message"] = str(e)
            test_result["details"] = f"无效的API请求: {str(e)}"
            logger.warning(f"LLM配置 '{config_id}' 测试失败 (无效请求): {e}")
        except APITimeoutError as e:
            test_result["status"] = "failed"
            test_result["error_type"] = "APITimeoutError"
            test_result["error_message"] = str(e)
            test_result["details"] = f"API请求超时: {str(e)}"
            logger.warning(f"LLM配置 '{config_id}' 测试失败 (API超时): {e}")
        except LLMAPIError as e: # 通用LLM API错误
            test_result["status"] = "failed"
            test_result["error_type"] = "LLMAPIError"
            test_result["error_message"] = str(e)
            test_result["details"] = f"LLM API通信错误: {str(e)}"
            logger.warning(f"LLM配置 '{config_id}' 测试失败 (LLM API错误): {e}")
        except Exception as e: # 其他意外错误
            test_result["status"] = "error"
            test_result["error_type"] = e.__class__.__name__
            test_result["error_message"] = str(e)
            test_result["details"] = f"测试过程中发生意外错误: {str(e)}"
            logger.error(f"LLM配置 '{config_id}' 测试时发生意外错误: {e}", exc_info=True)
        
        return test_result

    # 检索算法配置管理
    async def create_retrieval_algorithm_config(
        self, config_in: common.RetrievalAlgorithmConfigInput
    ) -> common.RetrievalAlgorithmConfig:
        # Check for duplicate name
        existing_config = crud_algorithm_config.get_algorithm_config_by_name(self.db, name=config_in.name)
        if existing_config:
            logger.warning(f"Attempt to create retrieval algorithm config with duplicate name: {config_in.name}")
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=f"名为 '{config_in.name}' 的检索算法配置已存在"
            )
        
        logger.info(f"Creating new retrieval algorithm configuration: {config_in.name}")
        try:
            db_config = crud_algorithm_config.create_algorithm_config(db=self.db, config_in=config_in)
            logger.info(f"Successfully created retrieval algorithm config '{db_config.name}' with id {db_config.id}")
            return common.RetrievalAlgorithmConfig.model_validate(db_config)
        except Exception as e:
            logger.error(f"Error creating retrieval algorithm config '{config_in.name}': {e}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"创建检索算法配置 '{config_in.name}' 失败: {str(e)}"
            )

    async def get_retrieval_algorithm_config(
        self, config_id: str
    ) -> Optional[common.RetrievalAlgorithmConfig]:
        logger.debug(f"Fetching retrieval algorithm config with id: {config_id}")
        db_config = crud_algorithm_config.get_algorithm_config(db=self.db, config_id=config_id)
        if not db_config:
            logger.warning(f"Retrieval algorithm config with id '{config_id}' not found.")
            return None
        return common.RetrievalAlgorithmConfig.model_validate(db_config)

    async def get_all_retrieval_algorithm_configs(self) -> List[common.RetrievalAlgorithmConfig]:
        logger.debug("Fetching all retrieval algorithm configs")
        db_configs = crud_algorithm_config.get_algorithm_configs(db=self.db)
        return [common.RetrievalAlgorithmConfig.model_validate(c) for c in db_configs]

    async def update_retrieval_algorithm_config(
        self, config_id: str, config_update: common.RetrievalAlgorithmConfigInput
    ) -> Optional[common.RetrievalAlgorithmConfig]:
        logger.info(f"Updating retrieval algorithm config with id: {config_id}")
        # Check if the new name conflicts with an existing different config
        if config_update.name:
            existing_by_name = crud_algorithm_config.get_algorithm_config_by_name(self.db, name=config_update.name)
            if existing_by_name and existing_by_name.id != config_id:
                logger.warning(f"Update failed: retrieval algorithm config name '{config_update.name}' already exists for id {existing_by_name.id}")
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=f"更新失败：名称为 '{config_update.name}' 的检索算法配置已存在。"
                )

        db_config = crud_algorithm_config.update_algorithm_config(
            db=self.db, config_id=config_id, config_update=config_update
        )
        if not db_config:
            logger.warning(f"Update failed: retrieval algorithm config with id '{config_id}' not found.")
            return None
        logger.info(f"Successfully updated retrieval algorithm config '{db_config.name}' (id: {db_config.id})")
        return common.RetrievalAlgorithmConfig.model_validate(db_config)

    async def delete_retrieval_algorithm_config(self, config_id: str) -> bool:
        logger.info(f"Attempting to delete retrieval algorithm config with id: {config_id}")
        # Check for associated ExperimentTaskConfigurations
        # tasks_using_this_config = self.db.query(ExperimentTaskConfiguration).filter(ExperimentTaskConfiguration.algorithm_config_id == config_id).count()
        # if tasks_using_this_config > 0:
        #     logger.warning(f"Cannot delete algorithm config '{config_id}' as it is used by {tasks_using_this_config} experiment tasks.")
        #     raise HTTPException(
        #         status_code=status.HTTP_409_CONFLICT,
        #         detail=f"无法删除算法配置 '{config_id}'，因为它正被 {tasks_using_this_config} 个实验任务使用。请先解除关联或删除这些任务。"
        #     )
        
        deleted = crud_algorithm_config.delete_algorithm_config(db=self.db, config_id=config_id)
        if deleted:
            logger.info(f"Successfully deleted retrieval algorithm config with id: {config_id}")
        else:
            logger.warning(f"Deletion failed: retrieval algorithm config with id '{config_id}' not found or error during deletion.")
        return deleted

    async def test_retrieval_algorithm_config(self, config_id: str) -> Dict[str, Any]:
        logger.info(f"Testing retrieval algorithm configuration with id: {config_id}")
        algo_config_model = await self.get_retrieval_algorithm_config(config_id)
        if not algo_config_model:
            logger.warning(f"Test failed: Retrieval algorithm config '{config_id}' not found.")
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="检索算法配置未找到")

        test_results = {
            "config_id": config_id, 
            "name": algo_config_model.name, 
            "tests": []
        }

        # Test 1: Corpus Extraction API
        corpus_api_config = algo_config_model.corpus_extraction_api
        corpus_test_result = {
            "api_type": "corpus_extraction",
            "endpoint": str(corpus_api_config.endpoint),
            "method_configured": corpus_api_config.method,
            "test_method_used": corpus_api_config.method, # For test, we usually use the configured method or OPTIONS
            "status": "pending",
            "details": None,
            "status_code": None
        }
        try:
            request_method = corpus_api_config.method.upper()
            request_url = str(corpus_api_config.endpoint)
            request_headers = corpus_api_config.headers
            # 对于GET请求，params来自corpus_api_config.params
            # 对于POST等，json_payload来自corpus_api_config.params (如果API如此设计的话，或者为空)
            
            request_params_for_log = corpus_api_config.params # Log configured params

            # 日志记录请求详情 - 将 .debug 改为 .info
            log_payload_description = "Params" if request_method == "GET" else "Body"
            logger.info(
                f"[Test Connection - Corpus] Preparing request: Method={request_method}, URL={request_url}, "
                f"Headers={request_headers}, Configured {log_payload_description}={request_params_for_log}"
            )

            # 实际测试时，我们可能只发送一个HEAD或OPTIONS请求，或者一个非常轻量级的GET
            # 这里为了简单，我们按配置的method发送，但不带实际的业务参数，除非method是GET且params有内容
            test_payload_params = None
            test_payload_json = None

            if request_method == "GET":
                # 对于GET测试，如果配置中定义了参数，则使用它们进行测试
                    test_payload_params = corpus_api_config.params
                # 如果没有配置参数，则 test_payload_params 保持为 None，依赖API能处理无参数请求或返回相应错误
                # logger.info(f"[Test Connection - Corpus] GET request, using params for test: {test_payload_params}") # 可选的额外日志
            else: # POST, PUT etc.
                # 对于POST等方法，测试连接时通常发送空JSON体或特定测试标记
                test_payload_json = {} # Send empty JSON for non-GET test

            response = await self.http_client.request(
                method=request_method, # 使用配置的方法
                url=request_url,
                headers=request_headers,
                params=test_payload_params, # GET请求的查询参数
                json=test_payload_json    # POST/PUT等请求的JSON体
            )
            
            corpus_test_result["status_code"] = response.status_code
            if 200 <= response.status_code < 300:
                corpus_test_result["status"] = "success"
                corpus_test_result["details"] = f"成功连接到语料抽取API ({request_method} {request_url}) 并获得状态码 {response.status_code}."
            else:
                corpus_test_result["status"] = "failed"
                corpus_test_result["details"] = f"连接到语料抽取API ({request_method} {request_url}) 成功，但返回非成功状态码 {response.status_code}. 响应: {response.text[:200]}"
            logger.info(f"[Test Connection - Corpus] Test for {request_url} completed. Status: {corpus_test_result['status']}, Code: {response.status_code}")

        except httpx.TimeoutException as e:
            corpus_test_result["status"] = "error"
            corpus_test_result["details"] = f"请求超时: {str(e)}"
            logger.error(f"Corpus Extraction API test for {config_id} threw a timeout exception: {e}", exc_info=True)
        except httpx.RequestError as e:
            corpus_test_result["status"] = "error"
            corpus_test_result["details"] = f"请求错误: {str(e)}"
            logger.error(f"Corpus Extraction API test for {config_id} threw an exception: {e}", exc_info=True)
        except Exception as e:
            corpus_test_result["status"] = "error"
            corpus_test_result["details"] = f"发生意外错误: {str(e)}"
            logger.error(f"Corpus Extraction API test for {config_id} threw an unexpected exception: {e}", exc_info=True)
        test_results["tests"].append(corpus_test_result)

        # Test 2: Similarity Search API
        similarity_api_config = algo_config_model.similarity_search_api
        similarity_test_result = {
            "api_type": "similarity_search",
            "endpoint": str(similarity_api_config.endpoint),
            "method_configured": similarity_api_config.method,
            "test_method_used": similarity_api_config.method,
            "status": "pending",
            "details": None,
            "status_code": None
        }
        try:
            request_method = similarity_api_config.method.upper()
            request_url = str(similarity_api_config.endpoint)
            request_headers = similarity_api_config.headers
            # 对于相似度搜索，body通常包含 algorithm_name, top_k, question等
            request_body_for_log = similarity_api_config.body

            # 日志记录请求详情 - 将 .debug 改为 .info
            logger.info(
                f"[Test Connection - Similarity] Preparing request: Method={request_method}, URL={request_url}, "
                f"Headers={request_headers}, Configured Body={request_body_for_log}"
            )
            
            test_payload_params_sim = None
            test_payload_json_sim = None

            if request_method == "POST":
                # 确保从配置的body开始，然后添加或覆盖测试特定的字段
                test_payload_json_sim = similarity_api_config.body.copy() if similarity_api_config.body else {}
                if "query_text" not in test_payload_json_sim: 
                    test_payload_json_sim["query_text"] = "甲硝唑的不良反应？" # 添加默认查询
                    # 更新details以反映添加了默认query_text
                    if similarity_test_result["details"] is None: # 避免覆盖其他预设信息
                        similarity_test_result["details"] = "注意：为测试目的，已自动添加默认 query_text。"
                    else:
                        similarity_test_result["details"] += " (已自动添加默认 query_text)"
                # 移除其他可能导致简单连接测试失败的业务参数，例如 k (如果 simple 方法测试)
                # 但保留如 selection_method, top_n_candidates (如果API允许它们存在于简单连接测试中)
                # 这里我们假设API对于连接测试，可以接受这些额外参数，或者会忽略它们
            elif request_method == "GET":
                test_payload_params_sim = similarity_api_config.params # GET请求使用params

            response = await self.http_client.request(
                method=request_method,
                url=request_url,
                headers=request_headers,
                params=test_payload_params_sim, 
                json=test_payload_json_sim # 对于POST等，发送构造好的JSON
            )
            similarity_test_result["status_code"] = response.status_code
            if 200 <= response.status_code < 300:
                similarity_test_result["status"] = "success"
                similarity_test_result["details"] = f"成功连接到相似度检索API ({request_method} {request_url}) 并获得状态码 {response.status_code}."
            else:
                similarity_test_result["status"] = "failed"
                similarity_test_result["details"] = f"连接到相似度检索API ({request_method} {request_url}) 成功，但返回非成功状态码 {response.status_code}. 响应: {response.text[:200]}"
            logger.info(f"[Test Connection - Similarity] Test for {request_url} completed. Status: {similarity_test_result['status']}, Code: {response.status_code}")

        except httpx.TimeoutException as e:
            similarity_test_result["status"] = "error"
            similarity_test_result["details"] = f"请求超时: {str(e)}"
            logger.error(f"Similarity Search API test for {config_id} threw a timeout exception: {e}", exc_info=True)
        except httpx.RequestError as e:
            similarity_test_result["status"] = "error"
            similarity_test_result["details"] = f"请求错误: {str(e)}"
            logger.error(f"Similarity Search API test for {config_id} threw an exception: {e}", exc_info=True)
        except Exception as e:
            similarity_test_result["status"] = "error"
            similarity_test_result["details"] = f"发生意外错误: {str(e)}"
            logger.error(f"Similarity Search API test for {config_id} threw an unexpected exception: {e}", exc_info=True)
        test_results["tests"].append(similarity_test_result)
        
        overall_success = all(test["status"] == "success" for test in test_results["tests"])
        test_results["overall_status"] = "success" if overall_success else "failed_or_error"
        
        return test_results

    # 实验任务配置管理 (ExperimentTaskConfig)
    async def create_experiment_task_config(self, task_config_in: common.ExperimentTaskConfigInput) -> common.ExperimentTaskConfigResponse:
        """创建实验任务配置。
        
        Args:
            task_config_in: 实验任务配置输入对象
            
        Returns:
            common.ExperimentTaskConfigResponse: 创建的实验任务配置响应对象
        """
        # 检查名称是否唯一
        existing_by_name = self.db.query(ExperimentTaskConfiguration).filter(ExperimentTaskConfiguration.name == task_config_in.name).first()
        if existing_by_name:
            raise ValueError(f"实验任务配置名称 '{task_config_in.name}' 已存在.")

        db_task_config = crud_experiment_task_config.create_experiment_task_config(self.db, task_config_in)
        if not db_task_config:
            # CRUD中已处理关联ID不存在的情况，这里假设如果返回None则为其他创建错误
            raise ValueError("创建实验任务配置失败，可能由于关联配置ID无效或数据库错误。")
        
        # 转换为响应模型并填充名称
        task_config_dict = crud_experiment_task_config.get_experiment_task_config_with_names(self.db, db_task_config.id)
        if not task_config_dict:
             # 这不应该发生，因为我们刚刚创建了它
             raise ValueError("创建后无法检索实验任务配置以获取名称。")
        return common.ExperimentTaskConfigResponse(**task_config_dict)

    async def get_experiment_task_config_response(self, task_config_id: str) -> Optional[common.ExperimentTaskConfigResponse]:
        """获取单个实验任务配置（用于API响应，包含名称）。
        
        Args:
            task_config_id: 实验任务配置ID
            
        Returns:
            Optional[common.ExperimentTaskConfigResponse]: 实验任务配置响应对象，如不存在返回None
        """
        task_config_dict = crud_experiment_task_config.get_experiment_task_config_with_names(self.db, task_config_id)
        if task_config_dict:
            return common.ExperimentTaskConfigResponse(**task_config_dict)
        return None

    async def get_full_experiment_task_config(self, task_config_id: str) -> Optional[common.ExperimentTaskConfig]:
        """获取完整的实验任务配置（内部使用，不含名称解析）。
        
        Args:
            task_config_id: 实验任务配置ID
            
        Returns:
            Optional[common.ExperimentTaskConfig]: 实验任务配置对象，如不存在返回None
        """
        db_item = crud_experiment_task_config.get_experiment_task_config(self.db, task_config_id)
        if db_item:
            return common.ExperimentTaskConfig.model_validate(db_item)
        return None

    async def get_all_experiment_task_configs_response(self, skip: int = 0, limit: int = 100) -> List[common.ExperimentTaskConfigResponse]:
        """获取实验任务配置列表（用于API响应，包含名称）。
        
        Args:
            skip: 跳过的记录数
            limit: 返回的最大记录数
            
        Returns:
            List[common.ExperimentTaskConfigResponse]: 实验任务配置响应对象列表
        """
        task_config_dicts = crud_experiment_task_config.get_experiment_task_configs_with_names(self.db, skip=skip, limit=limit)
        return [common.ExperimentTaskConfigResponse(**tc_dict) for tc_dict in task_config_dicts]

    async def update_experiment_task_config(
        self, task_config_id: str, task_config_update: common.ExperimentTaskConfigInput
    ) -> Optional[common.ExperimentTaskConfigResponse]:
        """更新实验任务配置。
        
        Args:
            task_config_id: 实验任务配置ID
            task_config_update: 新的实验任务配置输入对象
            
        Returns:
            Optional[common.ExperimentTaskConfigResponse]: 更新后的实验任务配置响应对象，如不存在返回None
        """
        # 检查名称是否与另一个现有配置冲突
        existing_by_name = self.db.query(ExperimentTaskConfiguration).filter(ExperimentTaskConfiguration.name == task_config_update.name).first()
        if existing_by_name and existing_by_name.id != task_config_id:
            raise ValueError(f"另一个实验任务配置的名称 '{task_config_update.name}' 已存在.")

        updated_db_task_config = crud_experiment_task_config.update_experiment_task_config(
            self.db, task_config_id, task_config_update
        )
        if not updated_db_task_config:
            # 可能配置ID不存在，或关联ID无效
            # CRUD层现在返回None如果关联ID无效，所以这里需要区分
            original_config = crud_experiment_task_config.get_experiment_task_config(self.db, task_config_id)
            if not original_config:
                return None # 配置ID本身无效
            # 如果 original_config 存在，但 update 返回 None，则可能是关联ID无效
            raise ValueError("更新实验任务配置失败，可能由于关联的子配置ID无效。")

        # 转换为响应模型并填充名称
        task_config_dict = crud_experiment_task_config.get_experiment_task_config_with_names(self.db, updated_db_task_config.id)
        if not task_config_dict:
            raise ValueError("更新后无法检索实验任务配置以获取名称。")
        return common.ExperimentTaskConfigResponse(**task_config_dict)

    async def delete_experiment_task_config(self, task_config_id: str) -> bool:
        """删除实验任务配置，并移除关联的实验运行记录。
        
        Args:
            task_config_id: 要删除的实验任务配置ID。
            
        Returns:
            bool: 如果删除成功返回True，否则返回False。
        """
        db_task_config = crud_experiment_task_config.get_experiment_task_config(self.db, task_config_id)
        if not db_task_config:
            logger.warning(f"尝试删除不存在的实验任务配置ID: {task_config_id}")
            return False # 配置不存在

        try:
            # 1. 删除所有引用此任务配置的 ExperimentRun 记录
            num_deleted_runs = crud_experiment_run.delete_experiment_runs_by_task_config_id(self.db, task_config_id)
            logger.info(f"为实验任务配置 {task_config_id} 删除了 {num_deleted_runs} 条关联的实验运行记录。")

            # 2. 删除 ExperimentTaskConfiguration 本身
            # crud_experiment_task_config.delete_experiment_task_config 现在不检查依赖
            success = crud_experiment_task_config.delete_experiment_task_config(self.db, task_config_id)
            if success:
                logger.info(f"实验任务配置 {task_config_id} 已成功删除。")
            else:
                # 这通常不应该发生，因为我们已经获取了db_task_config
                # 除非在删除关联运行和删除配置本身之间发生了某些并发问题
                logger.error(f"尝试删除实验任务配置 {task_config_id} 时，CRUD操作返回失败，即使配置存在。")
            return success
        except Exception as e:
            logger.error(f"删除实验任务配置 {task_config_id} 或其关联的实验运行时发生错误: {e}", exc_info=True)
            self.db.rollback() # 确保回滚
            return False

# 工厂函数，用于依赖注入
# def get_config_manager_service(db: Session = Depends(get_db_session)) -> ConfigManagerService:
#     """获取配置管理服务实例。
#     
#     Args:
#         db: 数据库会话
#         
#     Returns:
#         ConfigManagerService: 服务实例
#     """
#     return ConfigManagerService(db) 