from fastapi import APIRouter, HTTPException, Body
import random
import string
from typing import List, Dict, Any, Optional

mock_router = APIRouter(prefix="/mock", tags=["mock"])

# 模拟语料库
MOCK_CORPUS = [
    {
        "id": f"doc_{i}",
        # "content" 字段将用于填充响应中的 "entity_description"
        "original_content": f"这是第{i}篇文档的内容。包含一些专业术语和知识点。这是一个示例文档，用于测试相似度检索算法。"
    }
    for i in range(1, 21)  # 创建20篇模拟文档
]

# 更新：模拟语料抽取API
@mock_router.post("/corpus/sample", response_model=List[Dict[str, Any]])
@mock_router.head("/corpus/sample") # HEAD通常不用于POST，但为了简单保留，或移除
async def get_random_corpus_sample(
    payload: Dict[str, Any] = Body(...) # 从请求体获取参数
):
    """模拟知识图谱系统的随机语料抽取API。
    期望请求体: {"sample_size": int}
    响应: List[{"id": str, "entity_description": str}]
    """
    sample_size = payload.get("sample_size", 10)
    
    if not isinstance(sample_size, int) or sample_size <= 0:
        raise HTTPException(status_code=400, detail="sample_size 必须是正整数。")

    actual_sample_size = min(sample_size, len(MOCK_CORPUS))
    selected_docs_raw = random.sample(MOCK_CORPUS, actual_sample_size)
    
    response_docs = []
    for doc_raw in selected_docs_raw:
        response_docs.append({
            "id": doc_raw["id"],
            "entity_description": doc_raw["original_content"] # 对应文档要求
        })
    return response_docs

@mock_router.post("/similarity/search")
@mock_router.head("/similarity/search")
async def similarity_search(
    payload: Dict[str, Any] = Body(...) # 从请求体获取参数
):
    """模拟相似度检索算法API。
    期望请求体包含: "question", "top_k", "algorithm_name", 可选 "distance_threshold"
    响应: List[{"id": str, "entity": {"description": str}, "distance": float}]
    """
    
    question = payload.get("question", "")
    top_k = int(payload.get("top_k", 3))
    algorithm_name = payload.get("algorithm_name", "mock_default_algo")
    # distance_threshold = payload.get("distance_threshold") # 可选，当前mock中未使用

    if not question:
        # 根据实际API行为，可以选择返回空列表或错误
        # raise HTTPException(status_code=400, detail="'question' 字段是必需的。")
        return []
    if not isinstance(top_k, int) or top_k <= 0:
        raise HTTPException(status_code=400, detail="top_k 必须是正整数。")

    # 简单的模拟相似度计算：随机选择文档并赋予随机相似度分数
    results = []
    # MOCK_CORPUS 中的文档现在有 'id' 和 'original_content'
    selected_docs_raw = random.sample(MOCK_CORPUS, min(top_k, len(MOCK_CORPUS)))
    
    for doc_raw in selected_docs_raw:
        results.append({
            "id": doc_raw["id"],
            "entity": { # 对应文档要求的嵌套结构
                "description": doc_raw["original_content"]
            },
            "distance": round(random.uniform(0.1, 1.0), 4)  # 模拟距离值，通常0-1或0-2
        })
    
    # 如果需要，可以在此模拟基于 distance_threshold 的过滤
    # if distance_threshold is not None:
    #     results = [r for r in results if r["distance"] <= distance_threshold] # 假设距离越小越好

    return results 