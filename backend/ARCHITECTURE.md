# 后端架构文档 - 相似度检索算法实验平台

本文档旨在阐明"相似度检索算法实验平台"后端系统的主要架构设计和组件交互。

## 1. 概览

后端采用基于 **Python** 的现代 Web 框架 **FastAPI** 构建，用于提供 RESTful API 服务。对于耗时的实验过程，集成了 **Celery** 作为分布式任务队列，以实现异步处理，确保 API 的快速响应。

数据持久化依赖 **SQLAlchemy** 作为 ORM 与关系型数据库（默认为 SQLite，可配置为其他数据库）交互。Pydantic 模型广泛用于数据校验、序列化以及 API 请求/响应体定义。

外部 `llm_api_integrator` 模块被用作统一的LLM（大语言模型）调用接口。

## 2. 核心技术栈

*   **Web 框架**: FastAPI
*   **异步任务队列**: Celery (配合 Redis 作为 Broker 和 Result Backend)
*   **数据库 ORM**: SQLAlchemy
*   **数据校验/序列化**: Pydantic
*   **HTTP 客户端**: httpx (用于调用外部服务)
*   **依赖管理**: `requirements.txt`

## 3. 目录结构与模块职责 (`backend/app`)

```
app/
├── __init__.py
├── main.py             # FastAPI 应用实例与全局配置
├── core/               # 核心配置、安全、数据库连接、Celery Worker 初始化
│   ├── __init__.py
│   ├── config.py       # 应用配置 (通过 Pydantic Settings 管理)
│   ├── database.py     # SQLAlchemy 引擎、SessionLocal、get_db 依赖
│   ├── security.py     # API 密钥认证相关
│   └── celery_worker.py# Celery 应用实例初始化与配置
├── models/             # 数据模型定义
│   ├── __init__.py
│   ├── common.py       # Pydantic 模型 (API 请求/响应, 服务间数据传递)
│   └── db_models.py    # SQLAlchemy ORM 模型 (数据库表结构)
├── apis/               # API 层，包含路由定义和依赖注入
│   ├── __init__.py
│   ├── deps.py         # FastAPI 依赖注入函数 (如 get_db, get_current_user, 服务注入等)
│   └── v1/             # API 版本 v1
│       ├── __init__.py # 聚合 v1 版本的路由
│       └── endpoints/  # 各功能模块的 API 端点 (路由处理函数)
│           ├── __init__.py
│           ├── llm_configs.py
│           ├── algorithm_configs.py
│           └── experiments_api.py
├── services/           # 业务逻辑服务层，封装核心功能
│   ├── __init__.py
│   ├── config_manager_service.py    # LLM 和算法配置管理
│   ├── api_caller_service.py        # 封装外部 API 调用 (LLM, 知识图谱, 检索算法)
│   ├── logger_service.py            # 实验日志记录服务
│   ├── result_storage_service.py    # 实验结果存储服务
│   └── experiment_orchestrator_service.py # 核心实验编排服务
├── crud/               # 数据库 CRUD (Create, Read, Update, Delete) 操作封装
│   ├── __init__.py
│   ├── crud_llm_config.py
│   └── crud_algorithm_config.py
├── tasks.py            # Celery 异步任务定义 (如实验执行任务)
└── utils/              # 通用工具函数
    ├── __init__.py
    └── objective_scorer.py # 客观评分计算工具 (如 IoU)
```

## 4. 请求处理流程 (详细)

1.  **API 请求**: 客户端通过 HTTP 请求访问 FastAPI 定义的 API 端点 (位于 `app/apis/v1/endpoints/`)。

2.  **依赖注入**: FastAPI 通过 `app/apis/deps.py` 中定义的依赖注入函数，为端点提供必要的资源：
    * `get_db`: 提供数据库会话
    * `get_authenticated_db`: 结合API密钥验证和数据库会话
    * `get_config_manager`: 提供配置管理服务，依赖于已认证的数据库会话
    * `get_api_caller`: 提供API调用服务
    * `get_logger`: 提供日志服务
    * `get_result_storage`: 提供结果存储服务
    * `get_experiment_orchestrator`: 提供实验编排器服务，整合了上述所有服务

3.  **服务层处理**: API 端点通过依赖注入获取所需服务实例：
    * 配置管理相关端点使用 `ConfigManagerService`
    * 实验执行相关端点使用 `ExperimentOrchestratorService`
    * 实验结果查询端点使用 `ResultStorageService`
    * 日志查询端点使用 `LoggerService`

4.  **服务层依赖关系**:
    * `ExperimentOrchestratorService` 依赖于：
      - `ConfigManagerService`
      - `ApiCallerService`
      - `LoggerService`
      - `ResultStorageService`
    * `ConfigManagerService` 依赖于：
      - 数据库会话 (`Session`)
    * `ApiCallerService` 使用 `httpx.AsyncClient`
    * `LoggerService` 使用配置的日志目录
    * `ResultStorageService` 使用配置的结果存储目录

5.  **异步任务处理**:
    * 实验运行请求通过 `experiments_api.py` 中的 `/run` 端点提交
    * 请求被转换为 Celery 任务 (`run_experiment_task`)
    * Celery 任务使用专门的工厂函数 `get_experiment_orchestrator_service_for_task` 创建服务实例
    * 任务状态和结果可通过 `/tasks/{task_id}/status` 端点查询

6.  **结果处理与存储**:
    * 实验结果以 JSON 格式存储在文件系统中
    * 实验日志以 JSONL 格式记录详细执行过程
    * 结果可通过多个端点查询：
      - `/reports/{run_id}`: 获取完整实验报告
      - `/reports`: 获取所有实验摘要
      - `/logs/{run_id}`: 下载实验日志
      - `/tasks/results/{identifier}`: 通过任务ID或运行ID获取结果

## 5. 配置管理 (`app/core/config.py`)

*   应用配置通过 `pydantic-settings` 的 `BaseSettings` 类进行管理。
*   配置项（如数据库 URL, API 密钥, Celery Broker URL, 日志/结果目录等）可以从环境变量或 `.env` 文件加载。
*   `settings` 对象在整个应用中可用于访问配置值。

## 6. 数据库交互

*   **ORM**: SQLAlchemy。
*   **数据库模型**: 定义在 `app/models/db_models.py` (继承自 SQLAlchemy 的 `Base`)。其中 `LLMConfiguration` 表包含 `api_key` 字段，用于明文存储与该LLM配置关联的API密钥，此密钥仅供内部服务调用时使用，不会通过API响应暴露。
*   **数据库会话**: `app/core/database.py` 中定义了 `engine` 和 `SessionLocal`。`get_db` 函数作为 FastAPI 依赖项，为每个请求提供一个数据库会话，并在请求结束时关闭。
*   **表创建**: `main.py` 中通过 `Base.metadata.create_all(bind=engine)` 来创建数据库表（如果尚不存在）。

## 7. 异步任务处理 (Celery)

*   **Celery 应用**: 在 `app/core/celery_worker.py` 中初始化和配置 (`celery_app`)。
*   **任务定义**: 具体的异步任务（如 `run_experiment_task`）在 `app/tasks.py` 中定义，使用 `@celery_app.task` 装饰器。
*   **任务调用**: API 端点通过 `.delay()` 或 `.apply_async()` 方法将任务发送到 Celery 队列。
*   **结果后端**: Celery 配置了结果后端 (Redis)，用于存储任务的状态和结果，API 端点可以查询这些信息。
*   **依赖管理**: 
    - Celery 任务在独立的 worker 进程中执行，需要独立创建依赖。
    - `get_experiment_orchestrator_service_for_task` 工厂函数专门用于在 Celery 任务中创建服务实例。
    - 工厂函数直接创建所需的服务实例，而不依赖 FastAPI 的依赖注入系统。

## 8. 核心服务 (`app/services/`)

*   **`ExperimentOrchestratorService`**: 
    - 核心服务，负责编排完整的实验流程
    - 流程包括：数据抽取、问答对生成、相似度检索、评分、答案预测、评估和报告汇总
    - 依赖于其他四个基础服务
    - 提供两种实例化方式：
      * FastAPI 依赖注入 (`get_experiment_orchestrator_service`)
      * Celery 任务工厂函数 (`get_experiment_orchestrator_service_for_task`)

*   **`ConfigManagerService`**: 
    - 管理 LLM 模型配置和检索算法配置。
    - 提供配置的 CRUD 操作。对于LLM配置，支持存储关联的API密钥（明文存储，仅内部使用）。
    - 支持配置连接测试功能（测试时会从数据库读取对应配置的API密钥）。

*   **`ApiCallerService`**: 
    - 封装对外部 API 的调用。
    - 通过 `llm_api_integrator` 调用 LLM。调用前，会通过 `ConfigManagerService` 根据LLM配置ID获取对应的API密钥。
    - 处理 HTTP 请求的错误和重试。

*   **`LoggerService`**: 
    - 提供实验过程中的日志记录功能
    - 使用 JSONL 格式存储日志
    - 为每个实验运行创建独立的日志文件
    - 支持结构化日志记录和查询

*   **`ResultStorageService`**: 
    - 负责实验运行报告的存储和检索
    - 支持完整报告和摘要信息的获取
    - 提供文件系统级别的持久化存储
    - 支持异步操作

## 9. 数据模型 (`app/models/`)

*   **`common.py`**: 包含大量 Pydantic 模型，用于：
    *   API 请求和响应体的数据结构和校验
    *   服务层之间传递的数据对象
    *   实验报告 (`ExperimentRunReport`) 和其摘要 (`ExperimentRunReportSummary`) 的结构
    *   实验过程中的中间数据结构（如 `QAItem`, `SearchResultItem` 等）

*   **`db_models.py`**: 包含 SQLAlchemy ORM 模型，定义了数据库表的结构：
    *   `LLMConfiguration`: LLM 模型配置表（包含 `api_key` 字段用于存储原始密钥）。
    *   `RetrievalAlgorithmConfiguration`: 检索算法配置表。
    *   `ExperimentRun`: 实验运行记录表

## 10. 安全

*   基本的 API 密钥认证机制实现在 `app/core/security.py` 中。
*   `app/apis/deps.py` 中的 `get_api_key` 和 `get_authenticated_db` 依赖项用于保护需要认证的端点。
*   所有配置相关的端点都需要通过 API 密钥认证。
*   API 密钥通过请求头 `X-API-Key` 传递。

## 11. 与 `llm_api_integrator` 的交互

*   `llm_api_integrator` 模块被视为一个独立的、可重用的外部库，位于 `backend` 目录下。
*   `ApiCallerService` 通过 `LLMFactory` 获取特定 provider 的客户端实例。调用时，API密钥从 `ConfigManagerService` 中根据所选LLM配置ID获取。
*   支持多种 LLM 提供商：
    - OpenAI
    - Anthropic
    - Google (Gemini)
    - 阿里云
*   统一的异步调用接口：`async_generate_response`
*   统一的错误处理机制，包括：
    - 认证错误
    - 速率限制
    - 模型不存在
    - 请求无效
    - 超时

## 12. 总结

该后端架构是一个分层清晰、模块化的系统。FastAPI 提供了高效的 API 开发体验，Celery 保证了耗时任务的异步执行，SQLAlchemy 和 Pydantic 则为数据处理和持久化提供了强大支持。服务层的依赖注入设计使得系统具有良好的可测试性和可维护性，同时支持同步和异步操作模式。整个系统的设计充分考虑了可扩展性，可以方便地添加新的功能模块或集成新的外部服务。 