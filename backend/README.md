# 相似度检索算法实验平台 - 后端

这是相似度检索算法实验平台的后端服务，基于FastAPI构建。

## 功能

该系统用于比较和评估不同的相似度检索算法效果，主要功能包括：

1. LLM模型配置管理
2. 检索算法配置管理
3. 实验运行和编排
4. 结果评估和报告

## 技术栈

- **框架**: FastAPI
- **数据库**: SQLite (可扩展至其他数据库)
- **依赖**: 见 `requirements.txt`

## 安装与运行

### 前置条件

- Python 3.8+

### 安装依赖

```bash
pip install -r requirements.txt
```

### 配置

1. 确保根目录有`.env` 并修改配置参数
2. 确保有适当的文件系统权限创建日志和结果目录
3. 配置数据库

### 运行开发服务器

```bash
cd backend
uvicorn app.main:app --reload

```
服务将在 `http://localhost:8000` 启动，可通过 `http://localhost:8000/docs` 访问API文档。

### 启动redis和Celeryworker

celery -A app.core.celery_worker.celery_app worker -l info -P solo
关于 Celery Worker 的并发模式：
既然 -P solo 模式可以工作，你可以继续在开发和测试时使用它，特别是如果你主要在 Windows 上工作。它避免了 prefork 池在 Windows 上可能遇到的复杂问题。
如果将来你需要更高的并发性（例如，同时处理多个实验请求），你可以考虑：
在 Linux 或 macOS 环境中部署 Celery worker，在这些环境中 prefork 池通常更稳定。
在 Windows 上使用 -P gevent 或 -P eventlet （需要安装相应的库：pip install gevent eventlet）。这些基于协程的并发池在 Windows 上的兼容性通常比 prefork 好，并且非常适合 I/O 密集型任务（你的实验流程中有很多网络调用 LLM 和外部 API 的操作，属于 I/O 密集型）。
启动命令示例：
celery -A app.core.celery_worker.celery_app worker -l info -P gevent -c 50 ( -c 是并发数，可以根据需要调整，对于 gevent/eventlet 可以设置得比较高)

## API概览

- **配置管理**:
  - `/api/v1/llm-configs`: LLM模型配置CRUD操作
  - `/api/v1/algorithm-configs`: 检索算法配置CRUD操作

- **实验相关**:
  - `/api/v1/experiments`: 实验执行和结果查询

  **提问模板：**

请根据以下上下文，生成一个问题及其相应的答案。
上下文：{{{context}}}
你的输出必须是一个有效的JSON对象，并且只包含 "question" 和 "answer" 两个键。不要包含任何其他文本、解释说明或Markdown格式。
JSON对象应严格遵循此格式：{"question": "您生成的问题放这里", "answer": "您生成的答案放这里"}

**回答” (Answer Predictor) 模式的提示模板**

    请根据以下问题和提供的相关上下文信息，回答问题。请直接给出答案文本，不要包含任何额外的解释、说明或JSON格式。\n\n    问题：\n    {{{question}}}\n\n    相关上下文信息：\n    {{{retrieved_context}}}\n\n    答案：

 **“评估” (Evaluator) 模式的提示模板**

请扮演一个严谨的评估专家。你需要根据以下提供的所有信息，对“模型2预测的答案”在不同维度上进行评分。\r\n\r\n原始语料上下文：\r\n{{{original_context}}}\r\n\r\n生成的问题：\r\n{{{question}}}\r\n\r\n模型1基于原始上下文生成的参考答案：\r\n{{{generated_answer_llm1}}}\r\n\r\n检索算法为问题找到的相关上下文摘要：\r\n{{{retrieved_context_summary}}}\r\n\r\n模型2基于检索上下文预测的答案（待评估的答案）：\r\n{{{predicted_answer_llm2}}}\r\n\r\n请针对“模型2预测的答案”给出以下维度的评分（每个维度0-10分，10分为最高）：\r\n1.  **上下文相关性 (context_relevance)**：模型2的答案与检索到的上下文内容的相关程度如何？\r\n2.  **信息覆盖度 (information_coverage)**：模型2的答案是否充分利用了检索上下文中的关键信息来回答问题？\r\n3.  **答案准确性 (answer_accuracy)**：结合原始语料和检索上下文来看，模型2的答案在事实层面上的准确性如何？\r\n\r\n你的输出必须是一个有效的JSON对象，且仅包含 "context_relevance", "information_coverage", 和 "answer_accuracy" 三个键，值为对应的0-10分的数字评分。不要添加任何额外的解释、说明或Markdown标记。\r\n例如：{\"context_relevance\": 8, \"information_coverage\": 7, \"answer_accuracy\": 9}

## 开发指南

详细的开发工作流程和任务拆解请参见项目设计文档。 