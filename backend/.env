# 后端应用程序配置

# FastAPI 项目配置
PROJECT_NAME="相似度检索算法实验平台后端"         # 项目名称
PROJECT_VERSION="0.1.0"                        # 项目版本
DEBUG=True                                     # 是否开启调试模式

# API Key 用于保护自定义 API 端点（配合 X-API-Key 请求头使用）
API_KEY="abcd"         # API 端点密钥

# 数据库配置（SQLAlchemy）
SQLALCHEMY_DATABASE_URI="sqlite:///D:/ALLapp/Sqlite/db/csrasql.db" # 数据库连接字符串（可替换为 PostgreSQL 等）

# Celery 配置（用于后台任务）
CELERY_BROKER_URL="redis://localhost:6379/0"      # Celery 消息中间件（需确保 Redis 正在运行）
CELERY_RESULT_BACKEND="redis://localhost:6379/0"  # Celery 结果存储后端

# 日志和结果目录
LOG_DIR="experiment_logs"                         # 日志目录
RESULTS_DIR="experiment_results"                  # 结果目录

# 外部服务 API 密钥及端点（开发时可选）
DEFAULT_OPENAI_API_KEY=""         # OpenAI API 密钥
KNOWLEDGE_GRAPH_API_ENDPOINT="知识图谱API端点" # 知识图谱 API 端点

# CORS 允许的来源
# BACKEND_CORS_ORIGINS="http://localhost:3000"    # 前端开发服务器地址