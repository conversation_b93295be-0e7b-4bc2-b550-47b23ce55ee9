{"run_id": "测试_13", "experiment_task_config_id": "a18a94d6-fc89-48b9-9786-356c4a7b09da", "algorithm_config": {"name": "更新后的API知识库配置2", "corpus_extraction_api": {"endpoint": "https://knowledge.public.yzint.cn/knowledge/v1/graph/entity/random", "method": "GET", "headers": {"X-Auth-Token": "corpus-secret-token-v2"}, "params": {"count": "2"}}, "similarity_search_api": {"endpoint": "http://ss.public.yzint.cn/api/select_chunks", "method": "POST", "headers": {"Content-Type": "application/json"}, "body": {"selection_method": "greedy", "top_n_candidates": 2}}, "id": "4e9128e2-6f53-45f6-adcb-5d8d4f5043d5", "created_at": "2025-05-19T05:51:43", "updated_at": "2025-05-27T08:52:19", "created_at_local_str": "2025-05-19T13:51:43+08:00", "updated_at_local_str": "2025-05-27T16:52:19+08:00"}, "corpus_extraction_api_raw_responses": [], "qa_generator_config": {"role": "qa_generator", "name": "提问", "provider": "deepseek", "model_name": "deepseek-chat", "prompt_template": "任务：从以下提供的上下文中，生成一个相关的问题和对应的答案，要求覆盖目标实体和关系实体。\\n输入上下文：\\n{{context}}\\n\\n输出格式要求：\\n必须严格返回一个JSON对象。此JSON对象必须包含两个键：\\\"question\\\" 和 \\\"answer\\\"。\\n不要在JSON对象之前或之后添加任何文本、注释或Markdown标记。\\n响应必须直接是一个可解析的JSON对象。\\n\\n示例：\\n{\\\"question\\\": \\\"示例问题\\\", \\\"answer\\\": \\\"示例答案\\\"}\\n\\n请根据以上要求和上下文生成纯JSON：", "additional_params": {}, "id": "9100bf2d-40e8-4995-b3cc-62712a7068e0", "created_at_local_str": "2025-05-19T12:19:20+08:00", "updated_at_local_str": null}, "answer_predictor_config": {"role": "answer_predictor", "name": "预测", "provider": "deepseek", "model_name": "deepseek-chat", "prompt_template": "基于以下检索到的上下文，请回答问题。\\n问题：{{question}}\\n上下文：\\n{{retrieved_context}}\\n\\n请直接给出答案（注意，只需给出纯文本的答案内容，不要包含任何JSON结构或Markdown标记）：", "additional_params": {}, "id": "bbde4f9b-07f7-4275-9e9e-26ef11e87582", "created_at_local_str": "2025-05-19T12:19:27+08:00", "updated_at_local_str": null}, "evaluator_config": {"role": "evaluator", "name": "评估", "provider": "deepseek", "model_name": "deepseek-chat", "prompt_template": "            你是一个评估机器人。请根据以下信息，对提供的“预测答案”进行打分。\n            原始问题：{{question}}\n            真实答案：{{generated_answer}}\n            预测答案：{{predicted_answer}}\n            检索上下文：\n            {{retrieved_context}}\n\n            请根据以下维度进行评估，并严格返回一个JSON对象，包含 context_relevance, information_coverage, 和 answer_accuracy 三个键，分值为0.0到1.0之间：\n            1. context_relevance: 预测答案与检索上下文的相关性。\n            2. information_coverage: 预测答案是否充分利用了上下文信息来回答问题（与真实答案相比）。\n            3. answer_accuracy: 预测答案本身的准确性（与真实答案相比，并参考上下文）。\n\n            JSON输出示例：\n            {\"context_relevance\": 0.8, \"information_coverage\": 0.7, \"answer_accuracy\": 0.9}\n\n            请提供评估JSON：", "additional_params": {}, "id": "dec1fd47-8079-43f1-bf37-b1c3c089e143", "created_at_local_str": "2025-05-19T12:16:04+08:00", "updated_at_local_str": null}, "raw_corpus_items": [{"id": "37234502921781248", "content": "参芪蛤蚧补浆的执行标准为部标中药成方制剂第四册WS3-B-0763-91。该标准规定了药品的质量控制和生产规范，确保其符合国家相关要求。", "metadata": {"entity_name": "部标中药成方制剂第四册WS3-B-0763-91", "entity_type": "执行标准", "all_vector_ids": ["457683239705547395"]}}, {"id": "37234502921846784", "content": "参芪蛤蚧补浆是一种OTC甲类中药制剂，通用名称为参芪蛤蚧补浆。其主要成分为党参、黄芪和蛤蚧，辅料为蔗糖。性状为棕红色的粘稠状液体，味甜。功能主治包括补肺益肾、益精助阳、益气定喘，用于治疗体弱气虚、精神倦怠、阴虚喘咳、虚痨消渴及阳萎等症。用法用量为口服，一次20毫升，一日2次。贮藏方法为密封并置于阴凉处（不超过20℃）。包装形式为药用聚酯瓶装，每瓶装150毫升。有效期为24个月，执行标准为部标中药成方制剂第四册WS3-B-0763-91，批准文号为国药准字Z45022399。该药品由广西大力神制药股份有限公司生产。", "metadata": {"entity_name": "参芪蛤蚧补浆", "entity_type": "drug", "all_vector_ids": ["457683239705547469"]}}], "generated_qa_items": [{"original_corpus_item_id": "37234502921781248,37234502921846784", "original_corpus_content": "目标实体 (Target):\\n  Name: \\\"部标中药成方制剂第四册WS3-B-0763-91\\\"\\n  Description: \\\"参芪蛤蚧补浆的执行标准为部标中药成方制剂第四册WS3-B-0763-91。该标准规定了药品的质量控制和生产规范，确保其符合国家相关要求。\\\"\\n\\n关联实体 (Source):\\n  Name: \\\"参芪蛤蚧补浆\\\"\\n  Description: \\\"参芪蛤蚧补浆是一种OTC甲类中药制剂，通用名称为参芪蛤蚧补浆。其主要成分为党参、黄芪和蛤蚧，辅料为蔗糖。性状为棕红色的粘稠状液体，味甜。功能主治包括补肺益肾、益精助阳、益气定喘，用于治疗体弱气虚、精神倦怠、阴虚喘咳、虚痨消渴及阳萎等症。用法用量为口服，一次20毫升，一日2次。贮藏方法为密封并置于阴凉处（不超过20℃）。包装形式为药用聚酯瓶装，每瓶装150毫升。有效期为24个月，执行标准为部标中药成方制剂第四册WS3-B-0763-91，批准文号为国药准字Z45022399。该药品由广西大力神制药股份有限公司生产。\\\"\\n\\n关系:\\n  目标实体 '部标中药成方制剂第四册WS3-B-0763-91' 与关联实体 '参芪蛤蚧补浆' 的关系是: 国药准字Z45022399是参芪蛤蚧补浆的批准文号，而部标中药成方制剂第四册WS3-B-0763-91是该药品的执行标准。\"\n", "question": "参芪蛤蚧补浆的执行标准是什么？", "generated_answer": "参芪蛤蚧补浆的执行标准是部标中药成方制剂第四册WS3-B-0763-91。", "question_entity_id": "37234502921846784", "human_answer": null, "human_rating": null, "human_feedback": null, "metadata": {}, "id": "78cafb51-b4dd-4149-a71c-e0105a707c79", "experiment_id": null}], "all_similarity_search_results": [{"id": "a0ae087e-6c96-4c01-abb8-8d63bc42ddfc", "question_item": {"original_corpus_item_id": "37234502921781248,37234502921846784", "original_corpus_content": "目标实体 (Target):\\n  Name: \\\"部标中药成方制剂第四册WS3-B-0763-91\\\"\\n  Description: \\\"参芪蛤蚧补浆的执行标准为部标中药成方制剂第四册WS3-B-0763-91。该标准规定了药品的质量控制和生产规范，确保其符合国家相关要求。\\\"\\n\\n关联实体 (Source):\\n  Name: \\\"参芪蛤蚧补浆\\\"\\n  Description: \\\"参芪蛤蚧补浆是一种OTC甲类中药制剂，通用名称为参芪蛤蚧补浆。其主要成分为党参、黄芪和蛤蚧，辅料为蔗糖。性状为棕红色的粘稠状液体，味甜。功能主治包括补肺益肾、益精助阳、益气定喘，用于治疗体弱气虚、精神倦怠、阴虚喘咳、虚痨消渴及阳萎等症。用法用量为口服，一次20毫升，一日2次。贮藏方法为密封并置于阴凉处（不超过20℃）。包装形式为药用聚酯瓶装，每瓶装150毫升。有效期为24个月，执行标准为部标中药成方制剂第四册WS3-B-0763-91，批准文号为国药准字Z45022399。该药品由广西大力神制药股份有限公司生产。\\\"\\n\\n关系:\\n  目标实体 '部标中药成方制剂第四册WS3-B-0763-91' 与关联实体 '参芪蛤蚧补浆' 的关系是: 国药准字Z45022399是参芪蛤蚧补浆的批准文号，而部标中药成方制剂第四册WS3-B-0763-91是该药品的执行标准。\"\n", "question": "参芪蛤蚧补浆的执行标准是什么？", "generated_answer": "参芪蛤蚧补浆的执行标准是部标中药成方制剂第四册WS3-B-0763-91。", "question_entity_id": "37234502921846784", "human_answer": null, "human_rating": null, "human_feedback": null, "metadata": {}, "id": "78cafb51-b4dd-4149-a71c-e0105a707c79", "experiment_id": null}, "retrieved_items": [], "ground_truth_ids": ["457683239705547469"], "selection_method": "greedy", "k_value": null}], "all_predicted_answers": [], "all_evaluation_scores": [], "all_iou_scores": [{"retrieved_ids": [], "ground_truth_ids": ["457683239705547469"], "intersection_count": 0, "union_count": 1, "iou": 0.0, "score": 0.0}], "all_objective_information_coverage_scores": [{"retrieved_ids": [], "ground_truth_ids": ["457683239705547469"], "intersection_count": 0, "ground_truth_count": 1, "coverage_ratio": 0.0, "score": 0.0}], "overall_avg_iou_score": null, "overall_avg_objective_information_coverage_score": null, "overall_avg_context_relevance": null, "overall_avg_information_coverage": null, "overall_avg_answer_accuracy": null, "full_log_path": "experiment_logs\\测试_13\\run_测试_13.jsonl", "status": "FAILED", "status_message": "实验执行失败: cannot access local variable 'current_question_item_for_search' where it is not associated with a value", "error_details": [], "created_at_local_str": "2025-05-27T16:57:04.359699+08:00", "updated_at_local_str": "2025-05-27T16:57:13.148892+08:00"}