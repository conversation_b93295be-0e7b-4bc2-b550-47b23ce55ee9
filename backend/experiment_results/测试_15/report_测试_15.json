{"run_id": "测试_15", "experiment_task_config_id": "a18a94d6-fc89-48b9-9786-356c4a7b09da", "algorithm_config": {"name": "更新后的API知识库配置2", "corpus_extraction_api": {"endpoint": "https://knowledge.public.yzint.cn/knowledge/v1/graph/entity/random", "method": "GET", "headers": {"X-Auth-Token": "corpus-secret-token-v2"}, "params": {"count": "2"}}, "similarity_search_api": {"endpoint": "http://ss.public.yzint.cn/api/select_chunks", "method": "POST", "headers": {"Content-Type": "application/json"}, "body": {"selection_method": "greedy", "top_n_candidates": 2}}, "id": "4e9128e2-6f53-45f6-adcb-5d8d4f5043d5", "created_at": "2025-05-19T05:51:43", "updated_at": "2025-05-27T08:52:19", "created_at_local_str": "2025-05-19T13:51:43+08:00", "updated_at_local_str": "2025-05-27T16:52:19+08:00"}, "corpus_extraction_api_raw_responses": [], "qa_generator_config": {"role": "qa_generator", "name": "提问", "provider": "deepseek", "model_name": "deepseek-chat", "prompt_template": "任务：从以下提供的上下文中，生成一个相关的问题和对应的答案，要求覆盖目标实体和关系实体。\\n输入上下文：\\n{{context}}\\n\\n输出格式要求：\\n必须严格返回一个JSON对象。此JSON对象必须包含两个键：\\\"question\\\" 和 \\\"answer\\\"。\\n不要在JSON对象之前或之后添加任何文本、注释或Markdown标记。\\n响应必须直接是一个可解析的JSON对象。\\n\\n示例：\\n{\\\"question\\\": \\\"示例问题\\\", \\\"answer\\\": \\\"示例答案\\\"}\\n\\n请根据以上要求和上下文生成纯JSON：", "additional_params": {}, "id": "9100bf2d-40e8-4995-b3cc-62712a7068e0", "created_at_local_str": "2025-05-19T12:19:20+08:00", "updated_at_local_str": null}, "answer_predictor_config": {"role": "answer_predictor", "name": "预测", "provider": "deepseek", "model_name": "deepseek-chat", "prompt_template": "基于以下检索到的上下文，请回答问题。\\n问题：{{question}}\\n上下文：\\n{{retrieved_context}}\\n\\n请直接给出答案（注意，只需给出纯文本的答案内容，不要包含任何JSON结构或Markdown标记）：", "additional_params": {}, "id": "bbde4f9b-07f7-4275-9e9e-26ef11e87582", "created_at_local_str": "2025-05-19T12:19:27+08:00", "updated_at_local_str": null}, "evaluator_config": {"role": "evaluator", "name": "评估", "provider": "deepseek", "model_name": "deepseek-chat", "prompt_template": "            你是一个评估机器人。请根据以下信息，对提供的“预测答案”进行打分。\n            原始问题：{{question}}\n            真实答案：{{generated_answer}}\n            预测答案：{{predicted_answer}}\n            检索上下文：\n            {{retrieved_context}}\n\n            请根据以下维度进行评估，并严格返回一个JSON对象，包含 context_relevance, information_coverage, 和 answer_accuracy 三个键，分值为0.0到1.0之间：\n            1. context_relevance: 预测答案与检索上下文的相关性。\n            2. information_coverage: 预测答案是否充分利用了上下文信息来回答问题（与真实答案相比）。\n            3. answer_accuracy: 预测答案本身的准确性（与真实答案相比，并参考上下文）。\n\n            JSON输出示例：\n            {\"context_relevance\": 0.8, \"information_coverage\": 0.7, \"answer_accuracy\": 0.9}\n\n            请提供评估JSON：", "additional_params": {}, "id": "dec1fd47-8079-43f1-bf37-b1c3c089e143", "created_at_local_str": "2025-05-19T12:16:04+08:00", "updated_at_local_str": null}, "raw_corpus_items": [{"id": "37206419927236608", "content": "每10丸重2g是龙鹿丸的规格信息，表示每10丸药物的重量为2克。该规格用于描述药品的剂量单位，便于临床使用和管理。", "metadata": {"entity_name": "每10丸重2g", "entity_type": "specification", "all_vector_ids": ["457683239705524335"]}}, {"id": "37206419927629824", "content": "龙鹿丸为黑色的浓缩水丸，气微腥。其功能主治为温肾壮阳、益气滋肾，用于元气亏虚、精神萎靡、食欲不振；男子阳衰、精寒无子、遗精阳痿、举而不坚；女子宫寒、久不孕育。主要成分为人参、鹿茸、淫羊藿、狗鞭、驴鞭、熟地黄、山茱萸、五味子（酒蒸）、海龙、附子（制）、补骨脂（盐水炙）、肉苁蓉、锁阳、巴戟天、枸杞子、麦冬、山药（麸炒）、当归、黄芪、白术（土炒）、茯苓、菟丝子、覆盆子和杜仲（炒炭），部分描述中还提到含有续断。本品为处方药，规格为每10丸重2克，用法用量为口服，一次3-5丸，一日3次。贮藏方法为密封保存，包装形式为铝塑板，每盒装20丸。有效期为36个月。", "metadata": {"entity_name": "龙鹿丸", "entity_type": "drug", "all_vector_ids": ["457683239705524442"]}}], "generated_qa_items": [{"original_corpus_item_id": "37206419927236608,37206419927629824", "original_corpus_content": "目标实体 (Target):\\n  Name: \\\"每10丸重2g\\\"\\n  Description: \\\"每10丸重2g是龙鹿丸的规格信息，表示每10丸药物的重量为2克。该规格用于描述药品的剂量单位，便于临床使用和管理。\\\"\\n\\n关联实体 (Source):\\n  Name: \\\"龙鹿丸\\\"\\n  Description: \\\"龙鹿丸为黑色的浓缩水丸，气微腥。其功能主治为温肾壮阳、益气滋肾，用于元气亏虚、精神萎靡、食欲不振；男子阳衰、精寒无子、遗精阳痿、举而不坚；女子宫寒、久不孕育。主要成分为人参、鹿茸、淫羊藿、狗鞭、驴鞭、熟地黄、山茱萸、五味子（酒蒸）、海龙、附子（制）、补骨脂（盐水炙）、肉苁蓉、锁阳、巴戟天、枸杞子、麦冬、山药（麸炒）、当归、黄芪、白术（土炒）、茯苓、菟丝子、覆盆子和杜仲（炒炭），部分描述中还提到含有续断。本品为处方药，规格为每10丸重2克，用法用量为口服，一次3-5丸，一日3次。贮藏方法为密封保存，包装形式为铝塑板，每盒装20丸。有效期为36个月。\\\"\\n\\n关系:\\n  目标实体 '每10丸重2g' 与关联实体 '龙鹿丸' 的关系是: 龙鹿丸的规格为每10丸重2克。\"\n", "question": "龙鹿丸的规格是什么？", "generated_answer": "龙鹿丸的规格是每10丸重2克。", "question_entity_id": "37206419927629824", "human_answer": null, "human_rating": null, "human_feedback": null, "metadata": {}, "id": "b498ec0e-fb2f-452b-9db2-a24802b1c322", "experiment_id": null}], "all_similarity_search_results": [{"id": "6a615d9b-ca10-4b03-a47b-460966121099", "question_item": {"original_corpus_item_id": "37206419927236608,37206419927629824", "original_corpus_content": "目标实体 (Target):\\n  Name: \\\"每10丸重2g\\\"\\n  Description: \\\"每10丸重2g是龙鹿丸的规格信息，表示每10丸药物的重量为2克。该规格用于描述药品的剂量单位，便于临床使用和管理。\\\"\\n\\n关联实体 (Source):\\n  Name: \\\"龙鹿丸\\\"\\n  Description: \\\"龙鹿丸为黑色的浓缩水丸，气微腥。其功能主治为温肾壮阳、益气滋肾，用于元气亏虚、精神萎靡、食欲不振；男子阳衰、精寒无子、遗精阳痿、举而不坚；女子宫寒、久不孕育。主要成分为人参、鹿茸、淫羊藿、狗鞭、驴鞭、熟地黄、山茱萸、五味子（酒蒸）、海龙、附子（制）、补骨脂（盐水炙）、肉苁蓉、锁阳、巴戟天、枸杞子、麦冬、山药（麸炒）、当归、黄芪、白术（土炒）、茯苓、菟丝子、覆盆子和杜仲（炒炭），部分描述中还提到含有续断。本品为处方药，规格为每10丸重2克，用法用量为口服，一次3-5丸，一日3次。贮藏方法为密封保存，包装形式为铝塑板，每盒装20丸。有效期为36个月。\\\"\\n\\n关系:\\n  目标实体 '每10丸重2g' 与关联实体 '龙鹿丸' 的关系是: 龙鹿丸的规格为每10丸重2克。\"\n", "question": "龙鹿丸的规格是什么？", "generated_answer": "龙鹿丸的规格是每10丸重2克。", "question_entity_id": "37206419927629824", "human_answer": null, "human_rating": null, "human_feedback": null, "metadata": {}, "id": "b498ec0e-fb2f-452b-9db2-a24802b1c322", "experiment_id": null}, "retrieved_items": [], "ground_truth_ids": ["457683239705524442"], "selection_method": "greedy", "k_value": null}], "all_predicted_answers": [{"similarity_search_result": {"id": "6a615d9b-ca10-4b03-a47b-460966121099", "question_item": {"original_corpus_item_id": "37206419927236608,37206419927629824", "original_corpus_content": "目标实体 (Target):\\n  Name: \\\"每10丸重2g\\\"\\n  Description: \\\"每10丸重2g是龙鹿丸的规格信息，表示每10丸药物的重量为2克。该规格用于描述药品的剂量单位，便于临床使用和管理。\\\"\\n\\n关联实体 (Source):\\n  Name: \\\"龙鹿丸\\\"\\n  Description: \\\"龙鹿丸为黑色的浓缩水丸，气微腥。其功能主治为温肾壮阳、益气滋肾，用于元气亏虚、精神萎靡、食欲不振；男子阳衰、精寒无子、遗精阳痿、举而不坚；女子宫寒、久不孕育。主要成分为人参、鹿茸、淫羊藿、狗鞭、驴鞭、熟地黄、山茱萸、五味子（酒蒸）、海龙、附子（制）、补骨脂（盐水炙）、肉苁蓉、锁阳、巴戟天、枸杞子、麦冬、山药（麸炒）、当归、黄芪、白术（土炒）、茯苓、菟丝子、覆盆子和杜仲（炒炭），部分描述中还提到含有续断。本品为处方药，规格为每10丸重2克，用法用量为口服，一次3-5丸，一日3次。贮藏方法为密封保存，包装形式为铝塑板，每盒装20丸。有效期为36个月。\\\"\\n\\n关系:\\n  目标实体 '每10丸重2g' 与关联实体 '龙鹿丸' 的关系是: 龙鹿丸的规格为每10丸重2克。\"\n", "question": "龙鹿丸的规格是什么？", "generated_answer": "龙鹿丸的规格是每10丸重2克。", "question_entity_id": "37206419927629824", "human_answer": null, "human_rating": null, "human_feedback": null, "metadata": {}, "id": "b498ec0e-fb2f-452b-9db2-a24802b1c322", "experiment_id": null}, "retrieved_items": [], "ground_truth_ids": ["457683239705524442"], "selection_method": "greedy", "k_value": null}, "predicted_answer_text": "未找到相关信息"}], "all_evaluation_scores": [{"context_relevance": 0.9, "information_coverage": 0.8, "answer_accuracy": 0.85}], "all_iou_scores": [{"retrieved_ids": [], "ground_truth_ids": ["457683239705524442"], "intersection_count": 0, "union_count": 1, "iou": 0.0, "score": 0.0}], "all_objective_information_coverage_scores": [{"retrieved_ids": [], "ground_truth_ids": ["457683239705524442"], "intersection_count": 0, "ground_truth_count": 1, "coverage_ratio": 0.0, "score": 0.0}], "overall_avg_iou_score": 0.0, "overall_avg_objective_information_coverage_score": 0.0, "overall_avg_context_relevance": 0.9, "overall_avg_information_coverage": 0.8, "overall_avg_answer_accuracy": 0.85, "full_log_path": "experiment_logs\\测试_15\\run_测试_15.jsonl", "status": "COMPLETED", "status_message": "实验成功完成。", "error_details": [], "created_at_local_str": "2025-05-27T16:59:30.605401+08:00", "updated_at_local_str": "2025-05-27T16:59:49.361017+08:00"}