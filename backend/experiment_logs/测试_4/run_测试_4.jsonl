{"timestamp": "2025-05-27T06:57:02.771198", "run_id": "测试_4", "step_name": "ExperimentStart", "details": {"message": "实验 测试_4 开始"}}
{"timestamp": "2025-05-27T06:57:02.922613", "run_id": "测试_4", "step_name": "ExperimentStart", "details": {"run_id": "测试_4", "experiment_task_config_id": "a18a94d6-fc89-48b9-9786-356c4a7b09da", "max_corpus_items_override": 1}}
{"timestamp": "2025-05-27T06:57:03.021921", "run_id": "测试_4", "step_name": "ConfigsLoaded", "details": {"algorithm_name": "更新后的API知识库配置2", "qa_generator_name": "提问", "answer_predictor_name": "预测", "evaluator_name": "评估", "actual_target_qa_pairs": 1}}
{"timestamp": "2025-05-27T06:57:03.100838", "run_id": "测试_4", "step_name": "EntityPairExtractionStart", "details": {"target_pair_count": 1, "endpoint": "https://knowledge.public.yzint.cn/knowledge/v1/graph/entity/random"}}
{"timestamp": "2025-05-27T06:57:03.376713", "run_id": "测试_4", "step_name": "CorpusPairAPI_RawResponse", "details": {"attempt_num": 1, "target_url": "https://knowledge.public.yzint.cn/knowledge/v1/graph/entity/random", "request_params_sent": {"count": "2"}, "response_data": {"code": 0, "success": true, "msg": "Operation successful", "timestamp": "1748329024251", "data": {"entities": [{"id": "37243443986661376", "docId": "37200959494979584", "name": "茶叶", "type": "成份", "description": "茶叶在文本块中作为药物“艳友茶”的成份之一被提及，其性状描述为“气香”，与其他成份共同构成该药茶的组成。茶叶在此药茶中起到辅助作用，与其它成份如白芍、三七、荷叶、笔管草、甜叶菊配合，实现清热解毒、活血化瘀及抑制血小板聚集、预防血栓形成的功能，用于高血压、动脉硬化、肥胖症等的辅助治疗。", "communityId": "0"}, {"id": "37243443987578880", "docId": "37200959494979584", "name": "艳友茶", "type": "drug", "description": "艳友茶是一种灰褐色的药茶，具有气香、味甘、微苦涩的特点。其成分为白芍、三七、荷叶、笔管草、甜叶菊和茶叶，功能主治包括清热解毒、活血化瘀、抑制血小板聚集和预防血栓形成，适用于高血压、动脉硬化和肥胖症等疾病的辅助治疗。规格为每包装2g，用法为开水冲泡服用，每次一袋（2g），每日服用2～3次。包装形式为滤纸袋并复合膜袋，贮藏方法为密封并防潮。有效期为36个月，执行标准为部颁标准WS3-B-1804-94，批准文号为国药准字Z11020289，由北京九发药业有限公司生产。", "communityId": "0"}], "relationships": [{"id": "37243455478464512", "source": "37243443987578880", "target": "37243443986661376", "sourceName": "艳友茶", "targetName": "茶叶", "relationship": "艳友茶包含茶叶作为其成分之一。", "relationshipStrength": 10}], "vectors": [[{"vector_id": "457683239705554517", "id": "37243443986661376", "description": "茶叶 - 成份：茶叶在文本块中作为药物“艳友茶”的成份之一被提及，其性状描述为“气香”，与其他成份共同构成该药茶的组成。茶叶在此药茶中起到辅助作用，与其它成份如白芍、三七、荷叶、笔管草、甜叶菊配合，实现清热解毒、活血化瘀及抑制血小板聚集、预防血栓形成的功能，用于高血压、动脉硬化、肥胖症等的辅助治疗。"}], [{"vector_id": "457683239705554578", "id": "37243443987578880", "knowledge_base_id": "37048403296813056", "docId": "37200959494979584", "description": "艳友茶 - drug：艳友茶是一种灰褐色的药茶，具有气香、味甘、微苦涩的特点。其成分为白芍、三七、荷叶、笔管草、甜叶菊和茶叶，功能主治包括清热解毒、活血化瘀、抑制血小板聚集和预防血栓形成，适用于高血压、动脉硬化和肥胖症等疾病的辅助治疗。规格为每包装2g，用法为开水冲泡服用，每次一袋（2g），每日服用2～3次。包装形式为滤纸袋并复合膜袋，贮藏方法为密封并防潮。有效期为36个月，执行标准为部颁标准WS3-B-1804-94，批准文号为国药准字Z11020289，由北京九发药业有限公司生产。"}]]}}}}
{"timestamp": "2025-05-27T06:57:03.379225", "run_id": "测试_4", "step_name": "EntityPairExtractionComplete", "details": {"final_pair_count": 1, "target_pair_count": 1, "total_unique_corpus_items_in_map": 2, "sample_pairs_preview": [{"e1_id": "37243443986661376", "e2_id": "37243443987578880", "rel": "艳友茶包含茶叶作为其成分之一。..."}]}}
{"timestamp": "2025-05-27T06:57:03.473824", "run_id": "测试_4", "step_name": "QAGenerationStart", "details": {"entity_pair_count": 1}}
{"timestamp": "2025-05-27T06:57:03.473824", "run_id": "测试_4", "step_name": "QAGeneration_ConstructedContext", "details": {"run_id": "测试_4", "original_ids": "37243443986661376,37243443987578880", "constructed_content_snippet": "目标实体 (Target):\\n  Name: \\\"茶叶\\\"\\n  Description: \\\"茶叶在文本块中作为药物“艳友茶”的成份之一被提及，其性状描述为“气香”，与其他成份共同构成该药茶的组成。茶叶在此药茶中起到辅助作用，与其它成份如白芍、三七、荷叶、笔管草、甜叶菊配合，实现清热解毒、活血化瘀及抑制血小板聚集、预防血栓形成的功能，用于高血压、动脉硬化、肥胖症等的辅助治疗。\\\"\\n\\n关联实体 (Source):\\n  Name: \\\"艳友茶\\\"\\n  Description: \\\"艳友茶是一种灰褐色的药茶，具有气香、味甘、微苦涩的特点。其成分为白芍、三七、荷叶、笔管草、甜叶菊和茶叶，功"}}
{"timestamp": "2025-05-27T06:57:11.982545", "run_id": "测试_4", "step_name": "QAGenerationStageComplete", "details": {"run_id": "测试_4", "generated_count": 1, "attempted_count": 1}}
{"timestamp": "2025-05-27T06:57:11.982994", "run_id": "测试_4", "step_name": "QAGenerationComplete", "details": {"generated_qa_count": 1}}
{"timestamp": "2025-05-27T06:57:12.384090", "run_id": "测试_4", "step_name": "SimilaritySearchPhaseStart", "details": {"num_qa_items": 1, "kb_algorithm": "未在body中配置"}}
{"timestamp": "2025-05-27T06:57:12.385084", "run_id": "测试_4", "step_name": "SimilaritySearch_Greedy_Start", "details": {"question": "艳友茶中包含哪些成分，其中茶叶的作用是什么？", "payload": {"selection_method": "greedy", "top_n_candidates": 2, "query_text": "艳友茶中包含哪些成分，其中茶叶的作用是什么？"}, "endpoint": "http://ss.public.yzint.cn/api/select_chunks"}}
{"timestamp": "2025-05-27T06:57:12.464452", "run_id": "测试_4", "step_name": "SimilaritySearchError_Greedy", "details": {"question": "艳友茶中包含哪些成分，其中茶叶的作用是什么？", "error": "外部API http://ss.public.yzint.cn/api/select_chunks 返回错误状态 502: <html>\r\n<head><title>502 Bad Gateway</title></head>\r\n<body>\r\n<center><h1>502 Bad Gateway</h1></center>\r\n<hr><center>openresty</center>\r\n</body>\r\n</html>\r\n"}}
{"timestamp": "2025-05-27T06:57:12.464452", "run_id": "测试_4", "step_name": "SimilaritySearchPhaseAbort", "details": {"question": "艳友茶中包含哪些成分，其中茶叶的作用是什么？", "message": "由于检索错误，中止后续QA项的相似度检索。"}}
{"timestamp": "2025-05-27T06:57:12.464452", "run_id": "测试_4", "step_name": "SimilaritySearchPhaseComplete", "details": {"processed_qa_count": 1, "total_similarity_results_collected": 0}}
{"timestamp": "2025-05-27T06:57:12.539667", "run_id": "测试_4", "step_name": "AnswerPredictionStart", "details": {"search_result_count": 0}}
{"timestamp": "2025-05-27T06:57:12.540383", "run_id": "测试_4", "step_name": "AnswerPredictionStageComplete", "details": {"predicted_answer_count": 0}}
{"timestamp": "2025-05-27T06:57:12.625624", "run_id": "测试_4", "step_name": "EvaluationStart", "details": {"predicted_answer_count": 0}}
{"timestamp": "2025-05-27T06:57:12.626622", "run_id": "测试_4", "step_name": "EvaluationStageComplete", "details": {"evaluated_item_count": 0}}
{"timestamp": "2025-05-27T06:57:12.627629", "run_id": "测试_4", "step_name": "ReportFinalizationStart", "details": {"run_id": "测试_4"}}
{"timestamp": "2025-05-27T06:57:12.627629", "run_id": "测试_4", "step_name": "ExperimentEnd", "details": {"run_id": "测试_4", "final_report_summary": {"avg_iou_score": null, "avg_objective_information_coverage_score": null, "avg_llm_context_relevance": null, "avg_llm_information_coverage": null, "avg_llm_answer_accuracy": null, "total_corpus_items_processed": 2, "total_qa_generated": 1, "total_items_evaluated_by_llm": 0}}}
{"timestamp": "2025-05-27T06:57:12.691309", "run_id": "测试_4", "step_name": "ReportSavedAndDBSummaryUpdated", "details": {"run_id": "测试_4", "report_path": "experiment_results\\测试_4\\report_测试_4.json"}}
