{"timestamp": "2025-05-27T06:49:33.411370", "run_id": "测试_3", "step_name": "ExperimentStart", "details": {"message": "实验 测试_3 开始"}}
{"timestamp": "2025-05-27T06:49:33.490953", "run_id": "测试_3", "step_name": "ExperimentStart", "details": {"run_id": "测试_3", "experiment_task_config_id": "a18a94d6-fc89-48b9-9786-356c4a7b09da", "max_corpus_items_override": 1}}
{"timestamp": "2025-05-27T06:49:33.579337", "run_id": "测试_3", "step_name": "ConfigsLoaded", "details": {"algorithm_name": "更新后的API知识库配置2", "qa_generator_name": "提问", "answer_predictor_name": "预测", "evaluator_name": "评估", "actual_target_qa_pairs": 1}}
{"timestamp": "2025-05-27T06:49:33.643533", "run_id": "测试_3", "step_name": "EntityPairExtractionStart", "details": {"target_pair_count": 1, "endpoint": "https://knowledge.public.yzint.cn/knowledge/v1/graph/entity/random"}}
{"timestamp": "2025-05-27T06:49:34.003463", "run_id": "测试_3", "step_name": "CorpusPairAPI_RawResponse", "details": {"attempt_num": 1, "target_url": "https://knowledge.public.yzint.cn/knowledge/v1/graph/entity/random", "request_params_sent": {"count": "2"}, "response_data": {"code": 0, "success": true, "msg": "Operation successful", "timestamp": "1748328574874", "data": {"entities": [{"id": "37246375754629120", "docId": "37200940301844480", "name": "牡丹皮", "type": "ingredient", "description": "牡丹皮是双丹颗粒的成份之一，与丹参共同组成该药物的成分。在文本块中未提及牡丹皮的具体药理作用或与其他成分的相互作用，但作为药物成份，它参与实现该药“活血化瘀，通络止痛”的功能主治。", "communityId": "0"}, {"id": "37246375755284480", "docId": "37200940301844480", "name": "双丹颗粒", "type": "drug", "description": "双丹颗粒是一种黄褐色的颗粒，气芳香，味微苦。其通用名称为双丹颗粒，属于活血化瘀类中药制剂，具有活血化瘀、通络止痛的功能主治，用于治疗心血瘀阻所致的胸痹心痛。主要成分为丹参和牡丹皮。规格为每袋装5克，用法用量为温开水冲服，一次1袋，一日2次。该药为处方药（Rx），医保类型为乙类。药理毒理显示本品可通过增加冠脉血流量、扩张冠脉血管、改善心肌供血并对缺血心肌起到保护作用。注意事项中提到孕妇应慎用。贮藏方法要求密封保存，包装形式为铝塑袋，每盒含5克/袋×12袋。有效期为24个月。", "communityId": "0"}], "relationships": [{"id": "37246385250140160", "source": "37246375755284480", "target": "37246375754629120", "sourceName": "双丹颗粒", "targetName": "牡丹皮", "relationship": "双丹颗粒由丹参和牡丹皮组成，其中牡丹皮为主要成分之一，共同发挥活血化瘀、通络止痛的功效。", "relationshipStrength": 10}], "vectors": [[{"vector_id": "457683239705556941", "id": "37246375754629120", "description": "牡丹皮 - ingredient：牡丹皮是双丹颗粒的成份之一，与丹参共同组成该药物的成分。在文本块中未提及牡丹皮的具体药理作用或与其他成分的相互作用，但作为药物成份，它参与实现该药“活血化瘀，通络止痛”的功能主治。"}], [{"vector_id": "457683239705557006", "id": "37246375755284480", "knowledge_base_id": "37048403296813056", "docId": "37200940301844480", "description": "双丹颗粒 - drug：双丹颗粒是一种黄褐色的颗粒，气芳香，味微苦。其通用名称为双丹颗粒，属于活血化瘀类中药制剂，具有活血化瘀、通络止痛的功能主治，用于治疗心血瘀阻所致的胸痹心痛。主要成分为丹参和牡丹皮。规格为每袋装5克，用法用量为温开水冲服，一次1袋，一日2次。该药为处方药（Rx），医保类型为乙类。药理毒理显示本品可通过增加冠脉血流量、扩张冠脉血管、改善心肌供血并对缺血心肌起到保护作用。注意事项中提到孕妇应慎用。贮藏方法要求密封保存，包装形式为铝塑袋，每盒含5克/袋×12袋。有效期为24个月。"}]]}}}}
{"timestamp": "2025-05-27T06:49:34.007226", "run_id": "测试_3", "step_name": "EntityPairExtractionComplete", "details": {"final_pair_count": 1, "target_pair_count": 1, "total_unique_corpus_items_in_map": 2, "sample_pairs_preview": [{"e1_id": "37246375754629120", "e2_id": "37246375755284480", "rel": "双丹颗粒由丹参和牡丹皮组成，其中牡丹皮为主要成分之一，共同发挥活血化瘀、通络止痛的功效。..."}]}}
{"timestamp": "2025-05-27T06:49:34.101928", "run_id": "测试_3", "step_name": "QAGenerationStart", "details": {"entity_pair_count": 1}}
{"timestamp": "2025-05-27T06:49:34.101928", "run_id": "测试_3", "step_name": "QAGeneration_ConstructedContext", "details": {"run_id": "测试_3", "original_ids": "37246375754629120,37246375755284480", "constructed_content_snippet": "目标实体 (Target):\\n  Name: \\\"牡丹皮\\\"\\n  Description: \\\"牡丹皮是双丹颗粒的成份之一，与丹参共同组成该药物的成分。在文本块中未提及牡丹皮的具体药理作用或与其他成分的相互作用，但作为药物成份，它参与实现该药“活血化瘀，通络止痛”的功能主治。\\\"\\n\\n关联实体 (Source):\\n  Name: \\\"双丹颗粒\\\"\\n  Description: \\\"双丹颗粒是一种黄褐色的颗粒，气芳香，味微苦。其通用名称为双丹颗粒，属于活血化瘀类中药制剂，具有活血化瘀、通络止痛的功能主治，用于治疗心血瘀阻所致的胸痹心痛。主要成分为丹参和牡丹皮。规格为每袋装5克，用法用量"}}
{"timestamp": "2025-05-27T06:49:42.840679", "run_id": "测试_3", "step_name": "QAGenerationStageComplete", "details": {"run_id": "测试_3", "generated_count": 1, "attempted_count": 1}}
{"timestamp": "2025-05-27T06:49:42.841838", "run_id": "测试_3", "step_name": "QAGenerationComplete", "details": {"generated_qa_count": 1}}
{"timestamp": "2025-05-27T06:49:42.933959", "run_id": "测试_3", "step_name": "SimilaritySearchPhaseStart", "details": {"num_qa_items": 1, "kb_algorithm": "未在body中配置"}}
{"timestamp": "2025-05-27T06:49:42.934957", "run_id": "测试_3", "step_name": "SimilaritySearch_Greedy_Start", "details": {"question": "牡丹皮在双丹颗粒中起什么作用？", "payload": {"selection_method": "greedy", "top_n_candidates": 2, "query_text": "牡丹皮在双丹颗粒中起什么作用？"}, "endpoint": "http://ss.public.yzint.cn/api/select_chunks"}}
{"timestamp": "2025-05-27T06:49:43.008859", "run_id": "测试_3", "step_name": "SimilaritySearchError_Greedy", "details": {"question": "牡丹皮在双丹颗粒中起什么作用？", "error": "外部API http://ss.public.yzint.cn/api/select_chunks 返回错误状态 502: <html>\r\n<head><title>502 Bad Gateway</title></head>\r\n<body>\r\n<center><h1>502 Bad Gateway</h1></center>\r\n<hr><center>openresty</center>\r\n</body>\r\n</html>\r\n"}}
{"timestamp": "2025-05-27T06:49:43.008859", "run_id": "测试_3", "step_name": "SimilaritySearchInfo_Simple_Skip", "details": {"question": "牡丹皮在双丹颗粒中起什么作用？", "message": "由于Greedy阶段未能返回有效的k，跳过Simple检索阶段。"}}
{"timestamp": "2025-05-27T06:49:43.008859", "run_id": "测试_3", "step_name": "SimilaritySearchPhaseComplete", "details": {"processed_qa_count": 1, "total_similarity_results_collected": 0}}
{"timestamp": "2025-05-27T06:49:43.093242", "run_id": "测试_3", "step_name": "AnswerPredictionStart", "details": {"search_result_count": 0}}
{"timestamp": "2025-05-27T06:49:43.094238", "run_id": "测试_3", "step_name": "AnswerPredictionStageComplete", "details": {"predicted_answer_count": 0}}
{"timestamp": "2025-05-27T06:49:43.182428", "run_id": "测试_3", "step_name": "EvaluationStart", "details": {"predicted_answer_count": 0}}
{"timestamp": "2025-05-27T06:49:43.183425", "run_id": "测试_3", "step_name": "EvaluationStageComplete", "details": {"evaluated_item_count": 0}}
{"timestamp": "2025-05-27T06:49:43.183425", "run_id": "测试_3", "step_name": "ReportFinalizationStart", "details": {"run_id": "测试_3"}}
{"timestamp": "2025-05-27T06:49:43.184423", "run_id": "测试_3", "step_name": "ExperimentEnd", "details": {"run_id": "测试_3", "final_report_summary": {"avg_iou_score": null, "avg_objective_information_coverage_score": null, "avg_llm_context_relevance": null, "avg_llm_information_coverage": null, "avg_llm_answer_accuracy": null, "total_corpus_items_processed": 2, "total_qa_generated": 1, "total_items_evaluated_by_llm": 0}}}
{"timestamp": "2025-05-27T06:49:43.283324", "run_id": "测试_3", "step_name": "ReportSavedAndDBSummaryUpdated", "details": {"run_id": "测试_3", "report_path": "experiment_results\\测试_3\\report_测试_3.json"}}
